"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhZGFyc1xcRGVza3RvcFxcRkxcXG1lZGljb3NcXG1lZGljb3MtZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcYXBpXFxuYXZpZ2F0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb24nO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1uYXZpZ2F0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/question-bank/question-list.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/text-with-images */ \"(app-pages-browser)/./src/components/ui/text-with-images.tsx\");\n/* harmony import */ var _components_ui_base64_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/base64-image */ \"(app-pages-browser)/./src/components/ui/base64-image.tsx\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction QuestionList(param) {\n    let { questions, onDifficultyChange, onQuestionDeleted } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [questionToDelete, setQuestionToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle delete button click\n    const handleDelete = (questionId)=>{\n        setQuestionToDelete(questionId);\n        setIsDeleteDialogOpen(true);\n    };\n    // Handle edit button click\n    const handleEdit = (questionId)=>{\n        router.push(\"/admin/edit-question/\".concat(questionId));\n    };\n    // Confirm delete action\n    const confirmDelete = async ()=>{\n        if (!questionToDelete) return;\n        try {\n            setIsDeleting(true);\n            await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_8__.deleteQuestion)(questionToDelete);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.toast)({\n                title: \"Success\",\n                description: \"Question deleted successfully\"\n            });\n            if (onQuestionDeleted) {\n                onQuestionDeleted() // Refresh the list\n                ;\n            }\n        } catch (error) {\n            console.error(\"Error deleting question:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.toast)({\n                title: \"Error\",\n                description: error.message || \"Failed to delete question\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsDeleting(false);\n            setIsDeleteDialogOpen(false);\n            setQuestionToDelete(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: questions.map((question)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center justify-between gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                variant: \"outline\",\n                                                className: \"font-normal\",\n                                                children: question.subject\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                variant: \"outline\",\n                                                className: \"font-normal\",\n                                                children: question.topic\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 19\n                                            }, this),\n                                            question.reviewStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: question.reviewStatus === \"approved\" ? \"bg-green-100 text-green-800 hover:bg-green-100\" : question.reviewStatus === \"rejected\" ? \"bg-red-100 text-red-800 hover:bg-red-100\" : \"bg-yellow-100 text-yellow-800 hover:bg-yellow-100\",\n                                                children: question.reviewStatus.charAt(0).toUpperCase() + question.reviewStatus.slice(1)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                                defaultValue: question.difficulty.toLowerCase(),\n                                                onValueChange: (value)=>onDifficultyChange(question.id, value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                        className: \"w-[110px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                            placeholder: \"Difficulty\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 111,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                value: \"easy\",\n                                                                children: \"Easy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                lineNumber: 114,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                value: \"medium\",\n                                                                children: \"Medium\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                lineNumber: 115,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                                value: \"hard\",\n                                                                children: \"Hard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                                lineNumber: 116,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-medium\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__.TextWithImages, {\n                                    text: question.text,\n                                    maxImageWidth: 400,\n                                    maxImageHeight: 300\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                children: question.options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start p-3 rounded-md border \".concat(option.text === question.correctAnswer ? \"border-green-500 bg-green-50\" : \"border-gray-200\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: option.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    option.text && !option.isImageOption && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_text_with_images__WEBPACK_IMPORTED_MODULE_6__.TextWithImages, {\n                                                            text: option.text,\n                                                            maxImageWidth: 200,\n                                                            maxImageHeight: 150\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    option.imageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: option.isImageOption ? \"\" : \"mt-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_base64_image__WEBPACK_IMPORTED_MODULE_7__.Base64Image, {\n                                                            src: option.imageUrl,\n                                                            alt: \"Option \".concat(option.label),\n                                                            maxWidth: 200,\n                                                            maxHeight: 150,\n                                                            className: \"border-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    option.isImageOption && !option.imageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500 italic\",\n                                                        children: \"Image option\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            }, question.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-list.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionList, \"+eRCk8z/as9+V+LvEE6361BEfA0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter\n    ];\n});\n_c = QuestionList;\nvar _c;\n$RefreshReg$(_c, \"QuestionList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/questions.ts":
/*!**********************************!*\
  !*** ./src/lib/api/questions.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createQuestion: () => (/* binding */ createQuestion),\n/* harmony export */   createQuestionWithImages: () => (/* binding */ createQuestionWithImages),\n/* harmony export */   deleteQuestion: () => (/* binding */ deleteQuestion),\n/* harmony export */   getQuestionById: () => (/* binding */ getQuestionById),\n/* harmony export */   getQuestions: () => (/* binding */ getQuestions),\n/* harmony export */   getQuestionsByDifficulty: () => (/* binding */ getQuestionsByDifficulty),\n/* harmony export */   getQuestionsBySubjectAndTopic: () => (/* binding */ getQuestionsBySubjectAndTopic),\n/* harmony export */   updateQuestion: () => (/* binding */ updateQuestion),\n/* harmony export */   updateQuestionWithImages: () => (/* binding */ updateQuestionWithImages)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:3000/api\" || 0;\n/**\n * Create a new question\n * @param questionData The question data\n * @returns The created question\n */ async function createQuestion(questionData) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        // Create a copy of the data to avoid modifying the original\n        const dataToSend = {\n            ...questionData\n        };\n        // Remove fields that should not be sent to the API\n        if (!dataToSend.explanation || dataToSend.explanation.trim() === '') {\n            delete dataToSend.explanation;\n        }\n        // Remove status and reviewStatus as they're rejected by the API\n        delete dataToSend.status;\n        delete dataToSend.reviewStatus;\n        // Set default type if not provided\n        if (!dataToSend.type) {\n            dataToSend.type = 'multiple-choice';\n        }\n        console.log(\"Sending question data:\", JSON.stringify(dataToSend, null, 2));\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/questions\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: JSON.stringify(dataToSend)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"API error response:\", errorData);\n            // Check if we have detailed validation errors\n            if (errorData.details) {\n                const errorMessages = Object.entries(errorData.details).map((param)=>{\n                    let [field, message] = param;\n                    return \"\".concat(field, \": \").concat(message);\n                }).join(', ');\n                throw new Error(errorMessages || errorData.message || \"Error: \".concat(response.status));\n            }\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error creating question:\", error);\n        throw error;\n    }\n}\n/**\n * Get all questions\n * @returns List of questions\n */ async function getQuestions() {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/questions\"), {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching questions:\", error);\n        throw error;\n    }\n}\n/**\n * Get a question by ID\n * @param id Question ID\n * @returns The question\n */ async function getQuestionById(id) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(id), {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question:\", error);\n        throw error;\n    }\n}\n/**\n * Update a question\n * @param id Question ID\n * @param questionData The updated question data\n * @returns The updated question\n */ async function updateQuestion(id, questionData) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(id), {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: JSON.stringify(questionData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error updating question:\", error);\n        throw error;\n    }\n}\n/**\n * Update a question with optional new images (PATCH method)\n * @param id Question ID\n * @param questionData The updated question data\n * @param questionImage Optional new question image file\n * @param optionImages Optional new option images\n * @returns The updated question\n */ async function updateQuestionWithImages(id, questionData, questionImage, optionImages) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const formData = new FormData();\n        // Add question data\n        formData.append('content', questionData.content);\n        // Add options as individual form fields\n        questionData.options.forEach((option, index)=>{\n            formData.append(\"options[\".concat(index, \"]\"), option);\n        });\n        formData.append('answer', questionData.answer);\n        formData.append('subjectId', questionData.subjectId);\n        formData.append('topicId', questionData.topicId);\n        formData.append('difficulty', questionData.difficulty);\n        formData.append('type', questionData.type || 'multiple-choice');\n        // Add createdBy field if provided\n        if (questionData.createdBy) {\n            formData.append('createdBy', questionData.createdBy);\n        }\n        // Only add explanation if it has a value\n        if (questionData.explanation && questionData.explanation.trim() !== '') {\n            formData.append('explanation', questionData.explanation);\n        }\n        // Add question image if provided\n        if (questionImage) {\n            formData.append('images', questionImage);\n        }\n        // Add option images if provided\n        if (optionImages) {\n            Object.entries(optionImages).forEach((param)=>{\n                let [key, file] = param;\n                if (file) {\n                    formData.append(\"optionImages[\".concat(key, \"]\"), file);\n                }\n            });\n        }\n        const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(id), {\n            method: \"PATCH\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: formData\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error updating question with images:\", error);\n        throw error;\n    }\n}\n/**\n * Delete a question\n * @param id Question ID\n * @returns The deleted question\n */ async function deleteQuestion(id) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(id), {\n            method: \"DELETE\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error deleting question:\", error);\n        throw error;\n    }\n}\n/**\n * Create a question with images\n * @param questionData The question data without imageUrls\n * @param questionImage Optional question image file\n * @param optionImages Optional map of option images\n * @returns The created question\n */ async function createQuestionWithImages(questionData, questionImage, optionImages) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const formData = new FormData();\n        // Add question data\n        formData.append('content', questionData.content);\n        // Add options as individual form fields\n        questionData.options.forEach((option, index)=>{\n            formData.append(\"options[\".concat(index, \"]\"), option);\n        });\n        formData.append('answer', questionData.answer);\n        formData.append('subjectId', questionData.subjectId);\n        formData.append('topicId', questionData.topicId);\n        formData.append('difficulty', questionData.difficulty);\n        // Add type field with default if not provided\n        formData.append('type', questionData.type || 'multiple-choice');\n        // Add createdBy field if provided\n        if (questionData.createdBy) {\n            formData.append('createdBy', questionData.createdBy);\n        }\n        // Only add explanation if it has a value\n        if (questionData.explanation && questionData.explanation.trim() !== '') {\n            formData.append('explanation', questionData.explanation);\n        }\n        // Add question image if provided\n        if (questionImage) {\n            formData.append('images', questionImage);\n        }\n        // Add option images if provided\n        if (optionImages) {\n            Object.entries(optionImages).forEach((param)=>{\n                let [key, file] = param;\n                if (file) {\n                    formData.append(\"optionImages[\".concat(key, \"]\"), file);\n                }\n            });\n        }\n        // Log form data entries for debugging\n        console.log(\"Form data entries:\");\n        for (const pair of formData.entries()){\n            console.log(pair[0], pair[1]);\n        }\n        const response = await fetch(\"\".concat(baseUrl, \"/questions\"), {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: formData\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"API error response:\", errorData);\n            // Check if we have detailed validation errors\n            if (errorData.details) {\n                const errorMessages = Object.entries(errorData.details).map((param)=>{\n                    let [field, message] = param;\n                    return \"\".concat(field, \": \").concat(message);\n                }).join(', ');\n                throw new Error(errorMessages || errorData.message || \"Error: \".concat(response.status));\n            }\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error creating question with images:\", error);\n        throw error;\n    }\n}\n/**\n * Get questions by subject and topic\n * @param subjectId Subject ID\n * @param topicId Topic ID\n * @returns List of questions\n */ async function getQuestionsBySubjectAndTopic(subjectId, topicId) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const url = \"\".concat(baseUrl, \"/questions?subjectId=\").concat(encodeURIComponent(subjectId), \"&topicId=\").concat(encodeURIComponent(topicId));\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching questions by subject and topic:\", error);\n        throw error;\n    }\n}\n/**\n * Get questions by difficulty\n * @param difficulty Difficulty level ('easy', 'medium', 'hard')\n * @returns List of questions\n */ async function getQuestionsByDifficulty(difficulty) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const url = \"\".concat(baseUrl, \"/questions?difficulty=\").concat(encodeURIComponent(difficulty));\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching \".concat(difficulty, \" questions:\"), error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/questions.ts\n"));

/***/ })

});