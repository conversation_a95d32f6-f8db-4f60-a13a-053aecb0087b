"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d4ff5c2c8c08\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFkYXJzXFxEZXNrdG9wXFxGTFxcbWVkaWNvc1xcbWVkaWNvcy1mcm9udGVuZFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZDRmZjVjMmM4YzA4XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/AuthContext.tsx":
/*!*********************************!*\
  !*** ./src/lib/AuthContext.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useSafeAuth: () => (/* binding */ useSafeAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,useSafeAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Listen for auth state changes\n            const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, {\n                \"AuthProvider.useEffect.unsubscribe\": async (user)=>{\n                    setUser(user);\n                    if (user) {\n                        // User is signed in\n                        // Get user role from localStorage (set during login/signup)\n                        const storedRole = localStorage.getItem(\"userRole\");\n                        console.log(\"AuthContext - Retrieved role from localStorage:\", storedRole);\n                        if (storedRole) {\n                            setUserRole(storedRole);\n                        } else {\n                            // If no role in localStorage but user is logged in, try to get it from backend\n                            try {\n                                console.log(\"No role in localStorage, trying to get from backend\");\n                                const token = await user.getIdToken();\n                                localStorage.setItem(\"firebaseToken\", token);\n                                // Try to authenticate with backend to get role\n                                const backendAuth = await (0,_api__WEBPACK_IMPORTED_MODULE_4__.loginWithFirebaseToken)();\n                                if (backendAuth && backendAuth.user && backendAuth.user.role) {\n                                    console.log(\"Got role from backend:\", backendAuth.user.role);\n                                    localStorage.setItem(\"userRole\", backendAuth.user.role);\n                                    setUserRole(backendAuth.user.role);\n                                }\n                            } catch (error) {\n                                console.error(\"Failed to get role from backend:\", error);\n                            }\n                        }\n                    } else {\n                        // User is signed out\n                        setUserRole(null);\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.unsubscribe\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const signUp = async (email, password, displayName)=>{\n        try {\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, email, password);\n            // Update the user's display name\n            if (userCredential.user) {\n                await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.updateProfile)(userCredential.user, {\n                    displayName: displayName\n                });\n                // Get Firebase token\n                await (0,_api__WEBPACK_IMPORTED_MODULE_4__.getFirebaseToken)(userCredential.user);\n            // Set default role\n            // Comment out the default role setting to allow backend to determine role\n            // localStorage.setItem(\"userRole\", UserRole.TEACHER);\n            // setUserRole(UserRole.TEACHER);\n            }\n        } catch (error) {\n            console.error(\"Error signing up:\", error);\n            throw error;\n        }\n    };\n    const login = async (email, password)=>{\n        try {\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, email, password);\n            // Get Firebase token\n            await (0,_api__WEBPACK_IMPORTED_MODULE_4__.getFirebaseToken)(userCredential.user);\n        // Note: We'll get the user role from the backend response in the login page\n        // and set it there, rather than here\n        } catch (error) {\n            console.error(\"Error logging in:\", error);\n            throw error;\n        }\n    };\n    const loginWithGoogle = async ()=>{\n        try {\n            const provider = new firebase_auth__WEBPACK_IMPORTED_MODULE_2__.GoogleAuthProvider();\n            const result = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithPopup)(_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, provider);\n            // Get Firebase token\n            await (0,_api__WEBPACK_IMPORTED_MODULE_4__.getFirebaseToken)(result.user);\n        // Note: We'll get the user role from the backend response in the login page\n        // and set it there, rather than here\n        } catch (error) {\n            console.error(\"Error signing in with Google:\", error);\n            throw error;\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_firebase__WEBPACK_IMPORTED_MODULE_3__.auth);\n            // Clear local storage\n            localStorage.removeItem('backendToken');\n            localStorage.removeItem('userRole');\n            localStorage.removeItem('firebaseToken');\n        } catch (error) {\n            console.error(\"Error logging out:\", error);\n            throw error;\n        }\n    };\n    const resetPassword = async (email)=>{\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.sendPasswordResetEmail)(_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, email);\n            // Optional: Notify backend about password reset request\n            try {\n                const baseUrl = \"http://localhost:3000/api\" || 0;\n                await fetch(\"\".concat(baseUrl, \"/auth/reset-password-request\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        email\n                    })\n                });\n            } catch (backendError) {\n                console.warn(\"Failed to notify backend about password reset:\", backendError);\n            }\n        } catch (error) {\n            console.error(\"Error resetting password:\", error);\n            throw error;\n        }\n    };\n    const handlePasswordResetCompletion = async ()=>{\n        try {\n            if (!user) {\n                throw new Error(\"No authenticated user found\");\n            }\n            // Get and store Firebase token\n            await (0,_api__WEBPACK_IMPORTED_MODULE_4__.getFirebaseToken)(user);\n            // Authenticate with backend\n            try {\n                const backendAuth = await (0,_api__WEBPACK_IMPORTED_MODULE_4__.loginWithFirebaseToken)();\n                if (backendAuth.accessToken) {\n                    localStorage.setItem(\"backendToken\", backendAuth.accessToken);\n                }\n            } catch (backendError) {\n                console.warn(\"Backend authentication after password reset failed:\", backendError);\n            }\n        } catch (error) {\n            console.error(\"Error handling password reset completion:\", error);\n        }\n    };\n    const updateUserRole = (role)=>{\n        localStorage.setItem(\"userRole\", role);\n        setUserRole(role);\n    };\n    const deleteAccount = async ()=>{\n        try {\n            const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_3__.auth.currentUser;\n            if (currentUser) {\n                await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.deleteUser)(currentUser);\n                // Clear local storage\n                localStorage.removeItem('backendToken');\n                localStorage.removeItem('userRole');\n                localStorage.removeItem('firebaseToken');\n            }\n        } catch (error) {\n            console.error(\"Error deleting account:\", error);\n            throw error;\n        }\n    };\n    const value = {\n        user,\n        userRole,\n        loading,\n        signUp,\n        login,\n        loginWithGoogle,\n        logout,\n        resetPassword,\n        setUserRole: updateUserRole,\n        handlePasswordResetCompletion,\n        deleteAccount\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\lib\\\\AuthContext.tsx\",\n        lineNumber: 231,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AuthProvider, \"q48W6cVkKdV45khIg/0akNYQAtY=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction useSafeAuth() {\n    _s2();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    return context || {\n        user: null,\n        userRole: null,\n        loading: true,\n        signUp: async ()=>{\n            throw new Error(\"AuthProvider not found\");\n        },\n        login: async ()=>{\n            throw new Error(\"AuthProvider not found\");\n        },\n        loginWithGoogle: async ()=>{\n            throw new Error(\"AuthProvider not found\");\n        },\n        logout: async ()=>{\n            throw new Error(\"AuthProvider not found\");\n        },\n        resetPassword: async ()=>{\n            throw new Error(\"AuthProvider not found\");\n        },\n        setUserRole: ()=>{\n            throw new Error(\"AuthProvider not found\");\n        },\n        handlePasswordResetCompletion: async ()=>{\n            throw new Error(\"AuthProvider not found\");\n        },\n        deleteAccount: async ()=>{\n            throw new Error(\"AuthProvider not found\");\n        }\n    };\n}\n_s2(useSafeAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvQXV0aENvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRThFO0FBWXZEO0FBQ1c7QUFFK0I7QUFnQmpFLE1BQU1pQiw0QkFBY2hCLG9EQUFhQSxDQUE4QmlCO0FBRXhELE1BQU1DLGVBQXdEO1FBQUMsRUFBRUMsUUFBUSxFQUFFOztJQUNoRixNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR2xCLCtDQUFRQSxDQUFjO0lBQzlDLE1BQU0sQ0FBQ21CLFVBQVVDLFlBQVksR0FBR3BCLCtDQUFRQSxDQUFnQjtJQUN4RCxNQUFNLENBQUNxQixTQUFTQyxXQUFXLEdBQUd0QiwrQ0FBUUEsQ0FBQztJQUV2Q0QsZ0RBQVNBO2tDQUFDO1lBQ1IsZ0NBQWdDO1lBQ2hDLE1BQU13QixjQUFjbkIsaUVBQWtCQSxDQUFDTSwyQ0FBSUE7c0RBQUUsT0FBT087b0JBQ2xEQyxRQUFRRDtvQkFFUixJQUFJQSxNQUFNO3dCQUNSLG9CQUFvQjt3QkFDcEIsNERBQTREO3dCQUM1RCxNQUFNTyxhQUFhQyxhQUFhQyxPQUFPLENBQUM7d0JBQ3hDQyxRQUFRQyxHQUFHLENBQUMsbURBQW1ESjt3QkFFL0QsSUFBSUEsWUFBWTs0QkFDZEosWUFBWUk7d0JBQ2QsT0FBTzs0QkFDTCwrRUFBK0U7NEJBQy9FLElBQUk7Z0NBQ0ZHLFFBQVFDLEdBQUcsQ0FBQztnQ0FDWixNQUFNQyxRQUFRLE1BQU1aLEtBQUthLFVBQVU7Z0NBQ25DTCxhQUFhTSxPQUFPLENBQUMsaUJBQWlCRjtnQ0FFdEMsK0NBQStDO2dDQUMvQyxNQUFNRyxjQUFjLE1BQU1wQiw0REFBc0JBO2dDQUNoRCxJQUFJb0IsZUFBZUEsWUFBWWYsSUFBSSxJQUFJZSxZQUFZZixJQUFJLENBQUNnQixJQUFJLEVBQUU7b0NBQzVETixRQUFRQyxHQUFHLENBQUMsMEJBQTBCSSxZQUFZZixJQUFJLENBQUNnQixJQUFJO29DQUMzRFIsYUFBYU0sT0FBTyxDQUFDLFlBQVlDLFlBQVlmLElBQUksQ0FBQ2dCLElBQUk7b0NBQ3REYixZQUFZWSxZQUFZZixJQUFJLENBQUNnQixJQUFJO2dDQUNuQzs0QkFDRixFQUFFLE9BQU9DLE9BQU87Z0NBQ2RQLFFBQVFPLEtBQUssQ0FBQyxvQ0FBb0NBOzRCQUNwRDt3QkFDRjtvQkFDRixPQUFPO3dCQUNMLHFCQUFxQjt3QkFDckJkLFlBQVk7b0JBQ2Q7b0JBRUFFLFdBQVc7Z0JBQ2I7O1lBRUE7MENBQU8sSUFBTUM7O1FBQ2Y7aUNBQUcsRUFBRTtJQUVMLE1BQU1ZLFNBQVMsT0FBT0MsT0FBZUMsVUFBa0JDO1FBQ3JELElBQUk7WUFDRixNQUFNQyxpQkFBaUIsTUFBTXRDLDZFQUE4QkEsQ0FBQ1MsMkNBQUlBLEVBQUUwQixPQUFPQztZQUV6RSxpQ0FBaUM7WUFDakMsSUFBSUUsZUFBZXRCLElBQUksRUFBRTtnQkFDdkIsTUFBTVgsNERBQWFBLENBQUNpQyxlQUFldEIsSUFBSSxFQUFFO29CQUN2Q3FCLGFBQWFBO2dCQUNmO2dCQUVBLHFCQUFxQjtnQkFDckIsTUFBTTNCLHNEQUFnQkEsQ0FBQzRCLGVBQWV0QixJQUFJO1lBRTFDLG1CQUFtQjtZQUNuQiwwRUFBMEU7WUFDMUUsc0RBQXNEO1lBQ3RELGlDQUFpQztZQUNuQztRQUNGLEVBQUUsT0FBT2lCLE9BQU87WUFDZFAsUUFBUU8sS0FBSyxDQUFDLHFCQUFxQkE7WUFDbkMsTUFBTUE7UUFDUjtJQUNGO0lBRUEsTUFBTU0sUUFBUSxPQUFPSixPQUFlQztRQUNsQyxJQUFJO1lBQ0YsTUFBTUUsaUJBQWlCLE1BQU1yQyx5RUFBMEJBLENBQUNRLDJDQUFJQSxFQUFFMEIsT0FBT0M7WUFFckUscUJBQXFCO1lBQ3JCLE1BQU0xQixzREFBZ0JBLENBQUM0QixlQUFldEIsSUFBSTtRQUUxQyw0RUFBNEU7UUFDNUUscUNBQXFDO1FBQ3ZDLEVBQUUsT0FBT2lCLE9BQU87WUFDZFAsUUFBUU8sS0FBSyxDQUFDLHFCQUFxQkE7WUFDbkMsTUFBTUE7UUFDUjtJQUNGO0lBRUEsTUFBTU8sa0JBQWtCO1FBQ3RCLElBQUk7WUFDRixNQUFNQyxXQUFXLElBQUluQyw2REFBa0JBO1lBQ3ZDLE1BQU1vQyxTQUFTLE1BQU1uQyw4REFBZUEsQ0FBQ0UsMkNBQUlBLEVBQUVnQztZQUUzQyxxQkFBcUI7WUFDckIsTUFBTS9CLHNEQUFnQkEsQ0FBQ2dDLE9BQU8xQixJQUFJO1FBRWxDLDRFQUE0RTtRQUM1RSxxQ0FBcUM7UUFDdkMsRUFBRSxPQUFPaUIsT0FBTztZQUNkUCxRQUFRTyxLQUFLLENBQUMsaUNBQWlDQTtZQUMvQyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQSxNQUFNVSxTQUFTO1FBQ2IsSUFBSTtZQUNGLE1BQU16QyxzREFBT0EsQ0FBQ08sMkNBQUlBO1lBQ2xCLHNCQUFzQjtZQUN0QmUsYUFBYW9CLFVBQVUsQ0FBQztZQUN4QnBCLGFBQWFvQixVQUFVLENBQUM7WUFDeEJwQixhQUFhb0IsVUFBVSxDQUFDO1FBQzFCLEVBQUUsT0FBT1gsT0FBTztZQUNkUCxRQUFRTyxLQUFLLENBQUMsc0JBQXNCQTtZQUNwQyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQSxNQUFNWSxnQkFBZ0IsT0FBT1Y7UUFDM0IsSUFBSTtZQUNGLE1BQU0vQixxRUFBc0JBLENBQUNLLDJDQUFJQSxFQUFFMEI7WUFFbkMsd0RBQXdEO1lBQ3hELElBQUk7Z0JBQ0YsTUFBTVcsVUFBVUMsMkJBQStCLElBQUksQ0FBMkI7Z0JBQzlFLE1BQU1HLE1BQU0sR0FBVyxPQUFSSixTQUFRLGlDQUErQjtvQkFDcERLLFFBQVE7b0JBQ1JDLFNBQVM7d0JBQUUsZ0JBQWdCO29CQUFtQjtvQkFDOUNDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQzt3QkFBRXBCO29CQUFNO2dCQUMvQjtZQUNGLEVBQUUsT0FBT3FCLGNBQWM7Z0JBQ3JCOUIsUUFBUStCLElBQUksQ0FBQyxrREFBa0REO1lBQ2pFO1FBQ0YsRUFBRSxPQUFPdkIsT0FBTztZQUNkUCxRQUFRTyxLQUFLLENBQUMsNkJBQTZCQTtZQUMzQyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQSxNQUFNeUIsZ0NBQWdDO1FBQ3BDLElBQUk7WUFDRixJQUFJLENBQUMxQyxNQUFNO2dCQUNULE1BQU0sSUFBSTJDLE1BQU07WUFDbEI7WUFFQSwrQkFBK0I7WUFDL0IsTUFBTWpELHNEQUFnQkEsQ0FBQ007WUFFdkIsNEJBQTRCO1lBQzVCLElBQUk7Z0JBQ0YsTUFBTWUsY0FBYyxNQUFNcEIsNERBQXNCQTtnQkFDaEQsSUFBSW9CLFlBQVk2QixXQUFXLEVBQUU7b0JBQzNCcEMsYUFBYU0sT0FBTyxDQUFDLGdCQUFnQkMsWUFBWTZCLFdBQVc7Z0JBQzlEO1lBQ0YsRUFBRSxPQUFPSixjQUFjO2dCQUNyQjlCLFFBQVErQixJQUFJLENBQUMsdURBQXVERDtZQUN0RTtRQUNGLEVBQUUsT0FBT3ZCLE9BQU87WUFDZFAsUUFBUU8sS0FBSyxDQUFDLDZDQUE2Q0E7UUFDN0Q7SUFDRjtJQUVBLE1BQU00QixpQkFBaUIsQ0FBQzdCO1FBQ3RCUixhQUFhTSxPQUFPLENBQUMsWUFBWUU7UUFDakNiLFlBQVlhO0lBQ2Q7SUFFQSxNQUFNOEIsZ0JBQWdCO1FBQ3BCLElBQUk7WUFDRixNQUFNQyxjQUFjdEQsMkNBQUlBLENBQUNzRCxXQUFXO1lBQ3BDLElBQUlBLGFBQWE7Z0JBQ2YsTUFBTXZELHlEQUFVQSxDQUFDdUQ7Z0JBQ2pCLHNCQUFzQjtnQkFDdEJ2QyxhQUFhb0IsVUFBVSxDQUFDO2dCQUN4QnBCLGFBQWFvQixVQUFVLENBQUM7Z0JBQ3hCcEIsYUFBYW9CLFVBQVUsQ0FBQztZQUMxQjtRQUNGLEVBQUUsT0FBT1gsT0FBTztZQUNkUCxRQUFRTyxLQUFLLENBQUMsMkJBQTJCQTtZQUN6QyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQSxNQUFNK0IsUUFBUTtRQUNaaEQ7UUFDQUU7UUFDQUU7UUFDQWM7UUFDQUs7UUFDQUM7UUFDQUc7UUFDQUU7UUFDQTFCLGFBQWEwQztRQUNiSDtRQUNBSTtJQUNGO0lBRUEscUJBQ0UsOERBQUNsRCxZQUFZcUQsUUFBUTtRQUFDRCxPQUFPQTtrQkFDMUJqRDs7Ozs7O0FBR1AsRUFBQztHQXZNWUQ7S0FBQUE7QUF5TU4sU0FBU29EOztJQUNkLE1BQU1DLFVBQVV0RSxpREFBVUEsQ0FBQ2U7SUFDM0IsSUFBSXVELFlBQVl0RCxXQUFXO1FBQ3pCLE1BQU0sSUFBSThDLE1BQU07SUFDbEI7SUFDQSxPQUFPUTtBQUNUO0lBTmdCRDtBQVFULFNBQVNFOztJQUNkLE1BQU1ELFVBQVV0RSxpREFBVUEsQ0FBQ2U7SUFDM0IsT0FBT3VELFdBQVc7UUFDaEJuRCxNQUFNO1FBQ05FLFVBQVU7UUFDVkUsU0FBUztRQUNUYyxRQUFRO1lBQWMsTUFBTSxJQUFJeUIsTUFBTTtRQUEwQjtRQUNoRXBCLE9BQU87WUFBYyxNQUFNLElBQUlvQixNQUFNO1FBQTBCO1FBQy9EbkIsaUJBQWlCO1lBQWMsTUFBTSxJQUFJbUIsTUFBTTtRQUEwQjtRQUN6RWhCLFFBQVE7WUFBYyxNQUFNLElBQUlnQixNQUFNO1FBQTBCO1FBQ2hFZCxlQUFlO1lBQWMsTUFBTSxJQUFJYyxNQUFNO1FBQTBCO1FBQ3ZFeEMsYUFBYTtZQUFRLE1BQU0sSUFBSXdDLE1BQU07UUFBMEI7UUFDL0RELCtCQUErQjtZQUFjLE1BQU0sSUFBSUMsTUFBTTtRQUEwQjtRQUN2RkcsZUFBZTtZQUFjLE1BQU0sSUFBSUgsTUFBTTtRQUEwQjtJQUN6RTtBQUNGO0lBZmdCUyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhZGFyc1xcRGVza3RvcFxcRkxcXG1lZGljb3NcXG1lZGljb3MtZnJvbnRlbmRcXHNyY1xcbGliXFxBdXRoQ29udGV4dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQge1xuICBVc2VyLFxuICBjcmVhdGVVc2VyV2l0aEVtYWlsQW5kUGFzc3dvcmQsXG4gIHNpZ25JbldpdGhFbWFpbEFuZFBhc3N3b3JkLFxuICBzaWduT3V0LFxuICBvbkF1dGhTdGF0ZUNoYW5nZWQsXG4gIHNlbmRQYXNzd29yZFJlc2V0RW1haWwsXG4gIHVwZGF0ZVByb2ZpbGUsXG4gIEdvb2dsZUF1dGhQcm92aWRlcixcbiAgc2lnbkluV2l0aFBvcHVwLFxuICBkZWxldGVVc2VyXG59IGZyb20gXCJmaXJlYmFzZS9hdXRoXCI7XG5pbXBvcnQgeyBhdXRoIH0gZnJvbSBcIi4vZmlyZWJhc2VcIjtcbmltcG9ydCB7IFVzZXJSb2xlIH0gZnJvbSBcIi4vY29uc3RhbnRzL2VudW1zXCI7XG5pbXBvcnQgeyBnZXRGaXJlYmFzZVRva2VuLCBsb2dpbldpdGhGaXJlYmFzZVRva2VuIH0gZnJvbSBcIi4vYXBpXCI7XG5cbmludGVyZmFjZSBBdXRoQ29udGV4dFR5cGUge1xuICB1c2VyOiBVc2VyIHwgbnVsbDtcbiAgdXNlclJvbGU6IFVzZXJSb2xlIHwgbnVsbDtcbiAgbG9hZGluZzogYm9vbGVhbjtcbiAgc2lnblVwOiAoZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZywgZGlzcGxheU5hbWU6IHN0cmluZykgPT4gUHJvbWlzZTx2b2lkPjtcbiAgbG9naW46IChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKSA9PiBQcm9taXNlPHZvaWQ+O1xuICBsb2dpbldpdGhHb29nbGU6ICgpID0+IFByb21pc2U8dm9pZD47XG4gIGxvZ291dDogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbiAgcmVzZXRQYXNzd29yZDogKGVtYWlsOiBzdHJpbmcpID0+IFByb21pc2U8dm9pZD47XG4gIHNldFVzZXJSb2xlOiAocm9sZTogVXNlclJvbGUpID0+IHZvaWQ7XG4gIGhhbmRsZVBhc3N3b3JkUmVzZXRDb21wbGV0aW9uOiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xuICBkZWxldGVBY2NvdW50OiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xufVxuXG5jb25zdCBBdXRoQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8QXV0aENvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuXG5leHBvcnQgY29uc3QgQXV0aFByb3ZpZGVyOiBSZWFjdC5GQzx7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfT4gPSAoeyBjaGlsZHJlbiB9KSA9PiB7XG4gIGNvbnN0IFt1c2VyLCBzZXRVc2VyXSA9IHVzZVN0YXRlPFVzZXIgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3VzZXJSb2xlLCBzZXRVc2VyUm9sZV0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBMaXN0ZW4gZm9yIGF1dGggc3RhdGUgY2hhbmdlc1xuICAgIGNvbnN0IHVuc3Vic2NyaWJlID0gb25BdXRoU3RhdGVDaGFuZ2VkKGF1dGgsIGFzeW5jICh1c2VyKSA9PiB7XG4gICAgICBzZXRVc2VyKHVzZXIpO1xuICAgICAgXG4gICAgICBpZiAodXNlcikge1xuICAgICAgICAvLyBVc2VyIGlzIHNpZ25lZCBpblxuICAgICAgICAvLyBHZXQgdXNlciByb2xlIGZyb20gbG9jYWxTdG9yYWdlIChzZXQgZHVyaW5nIGxvZ2luL3NpZ251cClcbiAgICAgICAgY29uc3Qgc3RvcmVkUm9sZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwidXNlclJvbGVcIik7XG4gICAgICAgIGNvbnNvbGUubG9nKFwiQXV0aENvbnRleHQgLSBSZXRyaWV2ZWQgcm9sZSBmcm9tIGxvY2FsU3RvcmFnZTpcIiwgc3RvcmVkUm9sZSk7XG4gICAgICAgIFxuICAgICAgICBpZiAoc3RvcmVkUm9sZSkge1xuICAgICAgICAgIHNldFVzZXJSb2xlKHN0b3JlZFJvbGUpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIC8vIElmIG5vIHJvbGUgaW4gbG9jYWxTdG9yYWdlIGJ1dCB1c2VyIGlzIGxvZ2dlZCBpbiwgdHJ5IHRvIGdldCBpdCBmcm9tIGJhY2tlbmRcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coXCJObyByb2xlIGluIGxvY2FsU3RvcmFnZSwgdHJ5aW5nIHRvIGdldCBmcm9tIGJhY2tlbmRcIik7XG4gICAgICAgICAgICBjb25zdCB0b2tlbiA9IGF3YWl0IHVzZXIuZ2V0SWRUb2tlbigpO1xuICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXCJmaXJlYmFzZVRva2VuXCIsIHRva2VuKTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgLy8gVHJ5IHRvIGF1dGhlbnRpY2F0ZSB3aXRoIGJhY2tlbmQgdG8gZ2V0IHJvbGVcbiAgICAgICAgICAgIGNvbnN0IGJhY2tlbmRBdXRoID0gYXdhaXQgbG9naW5XaXRoRmlyZWJhc2VUb2tlbigpO1xuICAgICAgICAgICAgaWYgKGJhY2tlbmRBdXRoICYmIGJhY2tlbmRBdXRoLnVzZXIgJiYgYmFja2VuZEF1dGgudXNlci5yb2xlKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiR290IHJvbGUgZnJvbSBiYWNrZW5kOlwiLCBiYWNrZW5kQXV0aC51c2VyLnJvbGUpO1xuICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcInVzZXJSb2xlXCIsIGJhY2tlbmRBdXRoLnVzZXIucm9sZSk7XG4gICAgICAgICAgICAgIHNldFVzZXJSb2xlKGJhY2tlbmRBdXRoLnVzZXIucm9sZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJGYWlsZWQgdG8gZ2V0IHJvbGUgZnJvbSBiYWNrZW5kOlwiLCBlcnJvcik7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBVc2VyIGlzIHNpZ25lZCBvdXRcbiAgICAgICAgc2V0VXNlclJvbGUobnVsbCk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH0pO1xuXG4gICAgcmV0dXJuICgpID0+IHVuc3Vic2NyaWJlKCk7XG4gIH0sIFtdKTtcblxuICBjb25zdCBzaWduVXAgPSBhc3luYyAoZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZywgZGlzcGxheU5hbWU6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB1c2VyQ3JlZGVudGlhbCA9IGF3YWl0IGNyZWF0ZVVzZXJXaXRoRW1haWxBbmRQYXNzd29yZChhdXRoLCBlbWFpbCwgcGFzc3dvcmQpO1xuXG4gICAgICAvLyBVcGRhdGUgdGhlIHVzZXIncyBkaXNwbGF5IG5hbWVcbiAgICAgIGlmICh1c2VyQ3JlZGVudGlhbC51c2VyKSB7XG4gICAgICAgIGF3YWl0IHVwZGF0ZVByb2ZpbGUodXNlckNyZWRlbnRpYWwudXNlciwge1xuICAgICAgICAgIGRpc3BsYXlOYW1lOiBkaXNwbGF5TmFtZVxuICAgICAgICB9KTtcblxuICAgICAgICAvLyBHZXQgRmlyZWJhc2UgdG9rZW5cbiAgICAgICAgYXdhaXQgZ2V0RmlyZWJhc2VUb2tlbih1c2VyQ3JlZGVudGlhbC51c2VyKTtcblxuICAgICAgICAvLyBTZXQgZGVmYXVsdCByb2xlXG4gICAgICAgIC8vIENvbW1lbnQgb3V0IHRoZSBkZWZhdWx0IHJvbGUgc2V0dGluZyB0byBhbGxvdyBiYWNrZW5kIHRvIGRldGVybWluZSByb2xlXG4gICAgICAgIC8vIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFwidXNlclJvbGVcIiwgVXNlclJvbGUuVEVBQ0hFUik7XG4gICAgICAgIC8vIHNldFVzZXJSb2xlKFVzZXJSb2xlLlRFQUNIRVIpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3Igc2lnbmluZyB1cDpcIiwgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGxvZ2luID0gYXN5bmMgKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlckNyZWRlbnRpYWwgPSBhd2FpdCBzaWduSW5XaXRoRW1haWxBbmRQYXNzd29yZChhdXRoLCBlbWFpbCwgcGFzc3dvcmQpO1xuICAgICAgXG4gICAgICAvLyBHZXQgRmlyZWJhc2UgdG9rZW5cbiAgICAgIGF3YWl0IGdldEZpcmViYXNlVG9rZW4odXNlckNyZWRlbnRpYWwudXNlcik7XG4gICAgICBcbiAgICAgIC8vIE5vdGU6IFdlJ2xsIGdldCB0aGUgdXNlciByb2xlIGZyb20gdGhlIGJhY2tlbmQgcmVzcG9uc2UgaW4gdGhlIGxvZ2luIHBhZ2VcbiAgICAgIC8vIGFuZCBzZXQgaXQgdGhlcmUsIHJhdGhlciB0aGFuIGhlcmVcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGxvZ2dpbmcgaW46XCIsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBsb2dpbldpdGhHb29nbGUgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHByb3ZpZGVyID0gbmV3IEdvb2dsZUF1dGhQcm92aWRlcigpO1xuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgc2lnbkluV2l0aFBvcHVwKGF1dGgsIHByb3ZpZGVyKTtcblxuICAgICAgLy8gR2V0IEZpcmViYXNlIHRva2VuXG4gICAgICBhd2FpdCBnZXRGaXJlYmFzZVRva2VuKHJlc3VsdC51c2VyKTtcbiAgICAgIFxuICAgICAgLy8gTm90ZTogV2UnbGwgZ2V0IHRoZSB1c2VyIHJvbGUgZnJvbSB0aGUgYmFja2VuZCByZXNwb25zZSBpbiB0aGUgbG9naW4gcGFnZVxuICAgICAgLy8gYW5kIHNldCBpdCB0aGVyZSwgcmF0aGVyIHRoYW4gaGVyZVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3Igc2lnbmluZyBpbiB3aXRoIEdvb2dsZTpcIiwgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGxvZ291dCA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgYXdhaXQgc2lnbk91dChhdXRoKTtcbiAgICAgIC8vIENsZWFyIGxvY2FsIHN0b3JhZ2VcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdiYWNrZW5kVG9rZW4nKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyUm9sZScpO1xuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2ZpcmViYXNlVG9rZW4nKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGxvZ2dpbmcgb3V0OlwiLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgcmVzZXRQYXNzd29yZCA9IGFzeW5jIChlbWFpbDogc3RyaW5nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHNlbmRQYXNzd29yZFJlc2V0RW1haWwoYXV0aCwgZW1haWwpO1xuICAgICAgXG4gICAgICAvLyBPcHRpb25hbDogTm90aWZ5IGJhY2tlbmQgYWJvdXQgcGFzc3dvcmQgcmVzZXQgcmVxdWVzdFxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9hcGknO1xuICAgICAgICBhd2FpdCBmZXRjaChgJHtiYXNlVXJsfS9hdXRoL3Jlc2V0LXBhc3N3b3JkLXJlcXVlc3RgLCB7XG4gICAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBlbWFpbCB9KSxcbiAgICAgICAgfSk7XG4gICAgICB9IGNhdGNoIChiYWNrZW5kRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKFwiRmFpbGVkIHRvIG5vdGlmeSBiYWNrZW5kIGFib3V0IHBhc3N3b3JkIHJlc2V0OlwiLCBiYWNrZW5kRXJyb3IpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgcmVzZXR0aW5nIHBhc3N3b3JkOlwiLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlUGFzc3dvcmRSZXNldENvbXBsZXRpb24gPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGlmICghdXNlcikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJObyBhdXRoZW50aWNhdGVkIHVzZXIgZm91bmRcIik7XG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIEdldCBhbmQgc3RvcmUgRmlyZWJhc2UgdG9rZW5cbiAgICAgIGF3YWl0IGdldEZpcmViYXNlVG9rZW4odXNlcik7XG4gICAgICBcbiAgICAgIC8vIEF1dGhlbnRpY2F0ZSB3aXRoIGJhY2tlbmRcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGJhY2tlbmRBdXRoID0gYXdhaXQgbG9naW5XaXRoRmlyZWJhc2VUb2tlbigpO1xuICAgICAgICBpZiAoYmFja2VuZEF1dGguYWNjZXNzVG9rZW4pIHtcbiAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcImJhY2tlbmRUb2tlblwiLCBiYWNrZW5kQXV0aC5hY2Nlc3NUb2tlbik7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGJhY2tlbmRFcnJvcikge1xuICAgICAgICBjb25zb2xlLndhcm4oXCJCYWNrZW5kIGF1dGhlbnRpY2F0aW9uIGFmdGVyIHBhc3N3b3JkIHJlc2V0IGZhaWxlZDpcIiwgYmFja2VuZEVycm9yKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGhhbmRsaW5nIHBhc3N3b3JkIHJlc2V0IGNvbXBsZXRpb246XCIsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgdXBkYXRlVXNlclJvbGUgPSAocm9sZTogVXNlclJvbGUpID0+IHtcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcInVzZXJSb2xlXCIsIHJvbGUpO1xuICAgIHNldFVzZXJSb2xlKHJvbGUpO1xuICB9O1xuXG4gIGNvbnN0IGRlbGV0ZUFjY291bnQgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGN1cnJlbnRVc2VyID0gYXV0aC5jdXJyZW50VXNlcjtcbiAgICAgIGlmIChjdXJyZW50VXNlcikge1xuICAgICAgICBhd2FpdCBkZWxldGVVc2VyKGN1cnJlbnRVc2VyKTtcbiAgICAgICAgLy8gQ2xlYXIgbG9jYWwgc3RvcmFnZVxuICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnYmFja2VuZFRva2VuJyk7XG4gICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyUm9sZScpO1xuICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnZmlyZWJhc2VUb2tlbicpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZGVsZXRpbmcgYWNjb3VudDpcIiwgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHZhbHVlID0ge1xuICAgIHVzZXIsXG4gICAgdXNlclJvbGUsXG4gICAgbG9hZGluZyxcbiAgICBzaWduVXAsXG4gICAgbG9naW4sXG4gICAgbG9naW5XaXRoR29vZ2xlLFxuICAgIGxvZ291dCxcbiAgICByZXNldFBhc3N3b3JkLFxuICAgIHNldFVzZXJSb2xlOiB1cGRhdGVVc2VyUm9sZSxcbiAgICBoYW5kbGVQYXNzd29yZFJlc2V0Q29tcGxldGlvbixcbiAgICBkZWxldGVBY2NvdW50XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8QXV0aENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0F1dGhDb250ZXh0LlByb3ZpZGVyPlxuICApO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXV0aCgpIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoQXV0aENvbnRleHQpO1xuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwidXNlQXV0aCBtdXN0IGJlIHVzZWQgd2l0aGluIGFuIEF1dGhQcm92aWRlclwiKTtcbiAgfVxuICByZXR1cm4gY29udGV4dDtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHVzZVNhZmVBdXRoKCkge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChBdXRoQ29udGV4dCk7XG4gIHJldHVybiBjb250ZXh0IHx8IHtcbiAgICB1c2VyOiBudWxsLFxuICAgIHVzZXJSb2xlOiBudWxsLFxuICAgIGxvYWRpbmc6IHRydWUsXG4gICAgc2lnblVwOiBhc3luYyAoKSA9PiB7IHRocm93IG5ldyBFcnJvcihcIkF1dGhQcm92aWRlciBub3QgZm91bmRcIikgfSxcbiAgICBsb2dpbjogYXN5bmMgKCkgPT4geyB0aHJvdyBuZXcgRXJyb3IoXCJBdXRoUHJvdmlkZXIgbm90IGZvdW5kXCIpIH0sXG4gICAgbG9naW5XaXRoR29vZ2xlOiBhc3luYyAoKSA9PiB7IHRocm93IG5ldyBFcnJvcihcIkF1dGhQcm92aWRlciBub3QgZm91bmRcIikgfSxcbiAgICBsb2dvdXQ6IGFzeW5jICgpID0+IHsgdGhyb3cgbmV3IEVycm9yKFwiQXV0aFByb3ZpZGVyIG5vdCBmb3VuZFwiKSB9LFxuICAgIHJlc2V0UGFzc3dvcmQ6IGFzeW5jICgpID0+IHsgdGhyb3cgbmV3IEVycm9yKFwiQXV0aFByb3ZpZGVyIG5vdCBmb3VuZFwiKSB9LFxuICAgIHNldFVzZXJSb2xlOiAoKSA9PiB7IHRocm93IG5ldyBFcnJvcihcIkF1dGhQcm92aWRlciBub3QgZm91bmRcIikgfSxcbiAgICBoYW5kbGVQYXNzd29yZFJlc2V0Q29tcGxldGlvbjogYXN5bmMgKCkgPT4geyB0aHJvdyBuZXcgRXJyb3IoXCJBdXRoUHJvdmlkZXIgbm90IGZvdW5kXCIpIH0sXG4gICAgZGVsZXRlQWNjb3VudDogYXN5bmMgKCkgPT4geyB0aHJvdyBuZXcgRXJyb3IoXCJBdXRoUHJvdmlkZXIgbm90IGZvdW5kXCIpIH1cbiAgfTtcbn1cblxuXG5cblxuXG5cblxuXG5cblxuXG5cblxuXG5cblxuXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiY3JlYXRlVXNlcldpdGhFbWFpbEFuZFBhc3N3b3JkIiwic2lnbkluV2l0aEVtYWlsQW5kUGFzc3dvcmQiLCJzaWduT3V0Iiwib25BdXRoU3RhdGVDaGFuZ2VkIiwic2VuZFBhc3N3b3JkUmVzZXRFbWFpbCIsInVwZGF0ZVByb2ZpbGUiLCJHb29nbGVBdXRoUHJvdmlkZXIiLCJzaWduSW5XaXRoUG9wdXAiLCJkZWxldGVVc2VyIiwiYXV0aCIsImdldEZpcmViYXNlVG9rZW4iLCJsb2dpbldpdGhGaXJlYmFzZVRva2VuIiwiQXV0aENvbnRleHQiLCJ1bmRlZmluZWQiLCJBdXRoUHJvdmlkZXIiLCJjaGlsZHJlbiIsInVzZXIiLCJzZXRVc2VyIiwidXNlclJvbGUiLCJzZXRVc2VyUm9sZSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwidW5zdWJzY3JpYmUiLCJzdG9yZWRSb2xlIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImNvbnNvbGUiLCJsb2ciLCJ0b2tlbiIsImdldElkVG9rZW4iLCJzZXRJdGVtIiwiYmFja2VuZEF1dGgiLCJyb2xlIiwiZXJyb3IiLCJzaWduVXAiLCJlbWFpbCIsInBhc3N3b3JkIiwiZGlzcGxheU5hbWUiLCJ1c2VyQ3JlZGVudGlhbCIsImxvZ2luIiwibG9naW5XaXRoR29vZ2xlIiwicHJvdmlkZXIiLCJyZXN1bHQiLCJsb2dvdXQiLCJyZW1vdmVJdGVtIiwicmVzZXRQYXNzd29yZCIsImJhc2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX1VSTCIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwiYmFja2VuZEVycm9yIiwid2FybiIsImhhbmRsZVBhc3N3b3JkUmVzZXRDb21wbGV0aW9uIiwiRXJyb3IiLCJhY2Nlc3NUb2tlbiIsInVwZGF0ZVVzZXJSb2xlIiwiZGVsZXRlQWNjb3VudCIsImN1cnJlbnRVc2VyIiwidmFsdWUiLCJQcm92aWRlciIsInVzZUF1dGgiLCJjb250ZXh0IiwidXNlU2FmZUF1dGgiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/AuthContext.tsx\n"));

/***/ })

});