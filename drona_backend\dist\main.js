"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const swagger_1 = require("@nestjs/swagger");
const common_1 = require("@nestjs/common");
const filters_1 = require("./common/filters");
const config_1 = require("@nestjs/config");
const schema_indexes_1 = require("./common/indexes/schema-indexes");
const path = require("path");
const express = require("express");
async function bootstrap() {
    const logger = new common_1.Logger('Bootstrap');
    try {
        logger.log('Applying MongoDB indexes...');
        (0, schema_indexes_1.addSchemaIndexes)();
        logger.log('MongoDB indexes applied successfully');
    }
    catch (error) {
        logger.error(`Failed to apply MongoDB indexes: ${error.message}`, error.stack);
    }
    const firebaseServiceAccountPath = process.env.FIREBASE_SERVICE_ACCOUNT_PATH;
    if (firebaseServiceAccountPath) {
        const fullPath = firebaseServiceAccountPath.startsWith('/')
            ? firebaseServiceAccountPath
            : path.join(process.cwd(), firebaseServiceAccountPath);
        try {
            const fs = require('fs');
            if (fs.existsSync(fullPath)) {
                logger.log(`Firebase service account file found at: ${fullPath}`);
            }
            else {
                logger.warn(`Firebase service account file not found at: ${fullPath}`);
                logger.warn('Firebase authentication may not work correctly.');
            }
        }
        catch (error) {
            logger.error(`Error checking Firebase service account file: ${error.message}`, error.stack);
        }
    }
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const configService = app.get(config_1.ConfigService);
    const maxJsonSize = configService.get('MAX_JSON_SIZE') || '50mb';
    const maxUrlEncodedSize = configService.get('MAX_URL_ENCODED_SIZE') || '50mb';
    app.use(express.json({ limit: maxJsonSize }));
    app.use(express.urlencoded({ limit: maxUrlEncodedSize, extended: true }));
    app.useStaticAssets(path.join(process.cwd(), 'uploads'), {
        prefix: '/uploads/',
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
        transformOptions: {
            enableImplicitConversion: true,
        },
    }));
    app.useGlobalFilters(new filters_1.AllExceptionsFilter());
    if (configService.get('ENABLE_CORS')) {
        const allowedOrigins = configService.get('ALLOWED_ORIGINS')?.split(',') || ['http://localhost:3001'];
        app.enableCors({
            origin: allowedOrigins,
            methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
            credentials: true,
            allowedHeaders: 'Content-Type, Accept, Authorization',
        });
        logger.log(`CORS enabled for origins: ${allowedOrigins.join(', ')}`);
    }
    const config = new swagger_1.DocumentBuilder()
        .setTitle('Drona Backend API')
        .setDescription('The Drona Backend API documentation for question paper generation and management')
        .setVersion('1.0')
        .addServer('http://localhost:3000/api', 'Local Development')
        .addServer('https://api.drona.example.com/api', 'Production')
        .addTag('Authentication', 'User authentication and registration endpoints')
        .addTag('Users', 'User management endpoints')
        .addTag('Colleges', 'College management endpoints')
        .addTag('Teachers', 'Teacher management endpoints')
        .addTag('Questions', 'Question management endpoints with image compression')
        .addTag('Question Papers', 'Question paper generation and management endpoints')
        .addTag('Analytics - College', 'College-level analytics endpoints')
        .addTag('Analytics - Super Admin', 'Platform-wide analytics endpoints')
        .addTag('App', 'Application health and status endpoints')
        .addBearerAuth({
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
    }, 'JWT-auth')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config, {
        deepScanRoutes: true,
        extraModels: [],
    });
    swagger_1.SwaggerModule.setup('api/docs', app, document, {
        explorer: true,
        swaggerOptions: {
            persistAuthorization: true,
            docExpansion: 'none',
            filter: true,
            showExtensions: true,
            showCommonExtensions: true,
        },
    });
    const apiPrefix = configService.get('API_PREFIX');
    if (apiPrefix) {
        app.setGlobalPrefix(apiPrefix);
    }
    await app.listen(configService.get('PORT') || 3000);
}
bootstrap();
//# sourceMappingURL=main.js.map