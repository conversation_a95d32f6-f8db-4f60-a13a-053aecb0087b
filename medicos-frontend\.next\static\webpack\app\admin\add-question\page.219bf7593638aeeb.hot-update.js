"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/add-question/page",{

/***/ "(app-pages-browser)/./src/components/ui/use-toast.ts":
/*!****************************************!*\
  !*** ./src/components/ui/use-toast.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useToast,toast auto */ \n// Create a simple event-based toast system\nconst toastEventTarget =  true ? new EventTarget() : 0;\nconst TOAST_ADD_EVENT = 'toast-add';\nconst TOAST_REMOVE_EVENT = 'toast-remove';\n// Global toast state\nlet toasts = [];\nlet listeners = [];\nfunction notifyListeners() {\n    listeners.forEach((listener)=>listener([\n            ...toasts\n        ]));\n}\n// Add a toast\nfunction addToast(toast) {\n    const id = toast.id || Math.random().toString(36).substring(2, 9);\n    const newToast = {\n        ...toast,\n        id\n    };\n    toasts = [\n        ...toasts,\n        newToast\n    ];\n    notifyListeners();\n    // Auto dismiss after 5 seconds\n    setTimeout(()=>{\n        removeToast(id);\n    }, 5000);\n    return id;\n}\n// Remove a toast\nfunction removeToast(id) {\n    toasts = toasts.filter((t)=>t.id !== id);\n    notifyListeners();\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(toasts);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useToast.useEffect\": ()=>{\n            // Add this component as a listener\n            listeners.push(setState);\n            // Initial state sync\n            setState([\n                ...toasts\n            ]);\n            // Cleanup\n            return ({\n                \"useToast.useEffect\": ()=>{\n                    listeners = listeners.filter({\n                        \"useToast.useEffect\": (listener)=>listener !== setState\n                    }[\"useToast.useEffect\"]);\n                }\n            })[\"useToast.useEffect\"];\n        }\n    }[\"useToast.useEffect\"], []);\n    return {\n        toast: (props)=>addToast(props),\n        dismiss: (id)=>{\n            if (id) {\n                removeToast(id);\n            } else {\n                // Dismiss all toasts if no ID is provided\n                toasts.forEach((t)=>t.id && removeToast(t.id));\n            }\n        },\n        toasts: state\n    };\n}\n// Standalone toast function\nconst toast = (props)=>{\n    return addToast(props);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/use-toast.ts\n"));

/***/ })

});