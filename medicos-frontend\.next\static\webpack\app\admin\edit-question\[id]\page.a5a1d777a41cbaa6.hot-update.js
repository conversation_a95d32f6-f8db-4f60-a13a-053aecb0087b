"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/edit-question/[id]/page",{

/***/ "(app-pages-browser)/./src/components/admin/edit-question-form.tsx":
/*!*****************************************************!*\
  !*** ./src/components/admin/edit-question-form.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditQuestionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/api/subjects */ \"(app-pages-browser)/./src/lib/api/subjects.ts\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/utils/imageUtils */ \"(app-pages-browser)/./src/utils/imageUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB\n;\nconst ACCEPTED_FILE_TYPES = [\n    \"image/jpeg\",\n    \"image/jpg\",\n    \"image/png\",\n    \"image/svg+xml\"\n];\n// Form schema with custom validation for options\nconst formSchema = zod__WEBPACK_IMPORTED_MODULE_17__.z.object({\n    subject: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a subject\"\n    }),\n    topic: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a topic\"\n    }),\n    questionText: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(5, {\n        message: \"Question must be at least 5 characters\"\n    }),\n    optionA: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionB: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionC: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionD: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    correctAnswer: zod__WEBPACK_IMPORTED_MODULE_17__.z.enum([\n        \"A\",\n        \"B\",\n        \"C\",\n        \"D\"\n    ], {\n        required_error: \"Please select the correct answer\"\n    }),\n    explanation: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    difficulty: zod__WEBPACK_IMPORTED_MODULE_17__.z.enum([\n        \"Easy\",\n        \"Medium\",\n        \"Hard\"\n    ], {\n        required_error: \"Please select a difficulty level\"\n    })\n});\nfunction EditQuestionForm(param) {\n    let { questionData, questionId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [questionImage, setQuestionImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [optionImages, setOptionImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: null,\n        B: null,\n        C: null,\n        D: null\n    });\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topics, setTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [optionValidationErrors, setOptionValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: false,\n        B: false,\n        C: false,\n        D: false\n    });\n    // Refs for file inputs\n    const questionImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const optionImageRefs = {\n        A: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        B: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        C: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        D: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null)\n    };\n    // Initialize form with question data\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(formSchema),\n        defaultValues: {\n            subject: \"\",\n            topic: \"\",\n            questionText: \"\",\n            optionA: \"\",\n            optionB: \"\",\n            optionC: \"\",\n            optionD: \"\",\n            correctAnswer: \"\",\n            explanation: \"\",\n            difficulty: \"\"\n        }\n    });\n    // Parse existing question data and populate form\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditQuestionForm.useEffect\": ()=>{\n            if (questionData && subjects.length > 0) {\n                var _questionData_options;\n                // Parse options from the question data\n                const parseOptions = {\n                    \"EditQuestionForm.useEffect.parseOptions\": ()=>{\n                        if (!questionData.options || questionData.options.length === 0) return [\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\"\n                        ];\n                        if (typeof questionData.options[0] === 'string') {\n                            if (questionData.options.length === 1 && questionData.options[0].includes(',')) {\n                                // Single comma-separated string\n                                const optionTexts = questionData.options[0].split(',');\n                                return optionTexts.map({\n                                    \"EditQuestionForm.useEffect.parseOptions\": (text)=>text.trim()\n                                }[\"EditQuestionForm.useEffect.parseOptions\"]).concat(Array(4 - optionTexts.length).fill(\"\")).slice(0, 4);\n                            } else {\n                                // Array of individual strings\n                                return questionData.options.concat(Array(4 - questionData.options.length).fill(\"\")).slice(0, 4);\n                            }\n                        }\n                        return [\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\"\n                        ];\n                    }\n                }[\"EditQuestionForm.useEffect.parseOptions\"];\n                const parsedOptions = parseOptions();\n                const newOptionImages = {\n                    A: null,\n                    B: null,\n                    C: null,\n                    D: null\n                };\n                // Check for base64 images in options and extract them\n                parsedOptions.forEach({\n                    \"EditQuestionForm.useEffect\": (option, index)=>{\n                        const optionKey = String.fromCharCode(65 + index); // A, B, C, D\n                        if (typeof option === 'string' && (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_16__.isBase64Image)(option)) {\n                            newOptionImages[optionKey] = (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_16__.ensureDataUrl)(option);\n                            parsedOptions[index] = \"\"; // Clear text since it's an image\n                        }\n                    }\n                }[\"EditQuestionForm.useEffect\"]);\n                setOptionImages(newOptionImages);\n                var _questionData_options_findIndex;\n                // Find correct answer letter\n                const answerIndex = (_questionData_options_findIndex = (_questionData_options = questionData.options) === null || _questionData_options === void 0 ? void 0 : _questionData_options.findIndex({\n                    \"EditQuestionForm.useEffect\": (opt)=>opt === questionData.answer\n                }[\"EditQuestionForm.useEffect\"])) !== null && _questionData_options_findIndex !== void 0 ? _questionData_options_findIndex : -1;\n                const correctAnswerLetter = answerIndex >= 0 ? String.fromCharCode(65 + answerIndex) : \"A\";\n                // Handle topicId - it could be a string ID or a populated object\n                const topicId = questionData.topicId ? typeof questionData.topicId === 'string' ? questionData.topicId : questionData.topicId._id : \"\";\n                // Set form values\n                form.reset({\n                    subject: questionData.subjectId._id,\n                    topic: topicId,\n                    questionText: questionData.content,\n                    optionA: parsedOptions[0] || \"\",\n                    optionB: parsedOptions[1] || \"\",\n                    optionC: parsedOptions[2] || \"\",\n                    optionD: parsedOptions[3] || \"\",\n                    correctAnswer: correctAnswerLetter,\n                    explanation: questionData.explanation || \"\",\n                    difficulty: questionData.difficulty.charAt(0).toUpperCase() + questionData.difficulty.slice(1)\n                });\n                // Set topics for the selected subject\n                const selectedSubject = subjects.find({\n                    \"EditQuestionForm.useEffect.selectedSubject\": (s)=>s._id === questionData.subjectId._id\n                }[\"EditQuestionForm.useEffect.selectedSubject\"]);\n                if (selectedSubject) {\n                    setTopics(selectedSubject.topics || []);\n                }\n            }\n        }\n    }[\"EditQuestionForm.useEffect\"], [\n        questionData,\n        subjects,\n        form\n    ]);\n    // Fetch subjects and topics from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditQuestionForm.useEffect\": ()=>{\n            const fetchSubjectsAndTopics = {\n                \"EditQuestionForm.useEffect.fetchSubjectsAndTopics\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const data = await (0,_lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__.getSubjectsWithTopics)();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects and topics:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                            title: \"Error\",\n                            description: error.message || \"Failed to load subjects and topics\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"EditQuestionForm.useEffect.fetchSubjectsAndTopics\"];\n            fetchSubjectsAndTopics();\n        }\n    }[\"EditQuestionForm.useEffect\"], []);\n    // Handle subject change to update topics\n    const handleSubjectChange = (value)=>{\n        form.setValue(\"subject\", value);\n        form.setValue(\"topic\", \"\");\n        // Find the selected subject and set its topics\n        const selectedSubject = subjects.find((subject)=>subject._id === value);\n        if (selectedSubject) {\n            setTopics(selectedSubject.topics || []);\n        } else {\n            setTopics([]);\n        }\n    };\n    // Handle image upload\n    const handleImageUpload = (e, type)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            if (type === \"question\") {\n                var _event_target;\n                setQuestionImage((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result);\n            } else {\n                setOptionImages((prev)=>{\n                    var _event_target;\n                    return {\n                        ...prev,\n                        [type]: (_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result\n                    };\n                });\n                // Clear validation error for this option\n                setOptionValidationErrors((prev)=>({\n                        ...prev,\n                        [type]: false\n                    }));\n            }\n        };\n        reader.readAsDataURL(file);\n    };\n    // Remove image\n    const removeImage = (type)=>{\n        if (type === \"question\") {\n            setQuestionImage(null);\n            if (questionImageRef.current) {\n                questionImageRef.current.value = \"\";\n            }\n        } else {\n            var _optionImageRefs_type;\n            setOptionImages((prev)=>({\n                    ...prev,\n                    [type]: null\n                }));\n            if ((_optionImageRefs_type = optionImageRefs[type]) === null || _optionImageRefs_type === void 0 ? void 0 : _optionImageRefs_type.current) {\n                optionImageRefs[type].current.value = \"\";\n            }\n            // Clear form validation error for this option if it exists\n            form.clearErrors(\"option\".concat(type));\n            // Update validation state\n            setOptionValidationErrors((prev)=>({\n                    ...prev,\n                    [type]: false\n                }));\n        }\n    };\n    // Custom validation function\n    const validateOptions = (formData)=>{\n        const errors = [];\n        const validationState = {};\n        const options = [\n            'A',\n            'B',\n            'C',\n            'D'\n        ];\n        for (const option of options){\n            const hasText = formData[\"option\".concat(option)] && formData[\"option\".concat(option)].trim() !== '';\n            const hasImage = optionImages[option] !== null;\n            if (!hasText && !hasImage) {\n                errors.push(\"Option \".concat(option, \" must have either text or an image\"));\n                validationState[option] = true;\n            } else {\n                validationState[option] = false;\n            }\n        }\n        // Update validation state\n        setOptionValidationErrors(validationState);\n        return errors;\n    };\n    // Handle form submission\n    const onSubmit = async (data)=>{\n        setIsSubmitting(true);\n        try {\n            var _data_optionA, _data_optionB, _data_optionC, _data_optionD;\n            console.log(\"Form data:\", data);\n            // Validate that each option has either text or image\n            const validationErrors = validateOptions(data);\n            if (validationErrors.length > 0) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                    title: \"Validation Error\",\n                    description: validationErrors.join(', '),\n                    variant: \"destructive\"\n                });\n                setIsSubmitting(false);\n                return;\n            }\n            // Convert option data to array format expected by API\n            // Use image base64 if no text, otherwise use text\n            const options = [\n                ((_data_optionA = data.optionA) === null || _data_optionA === void 0 ? void 0 : _data_optionA.trim()) || optionImages.A || '',\n                ((_data_optionB = data.optionB) === null || _data_optionB === void 0 ? void 0 : _data_optionB.trim()) || optionImages.B || '',\n                ((_data_optionC = data.optionC) === null || _data_optionC === void 0 ? void 0 : _data_optionC.trim()) || optionImages.C || '',\n                ((_data_optionD = data.optionD) === null || _data_optionD === void 0 ? void 0 : _data_optionD.trim()) || optionImages.D || ''\n            ];\n            // Map correctAnswer (A, B, C, D) to the actual option value\n            const answerMap = {\n                A: 0,\n                B: 1,\n                C: 2,\n                D: 3\n            };\n            const answerIndex = answerMap[data.correctAnswer];\n            const answer = options[answerIndex];\n            // Convert difficulty to lowercase to match API expectations\n            const difficulty = data.difficulty.toLowerCase();\n            // Create base question data\n            const baseQuestionData = {\n                content: data.questionText,\n                options,\n                answer,\n                subjectId: data.subject,\n                topicId: data.topic,\n                difficulty,\n                type: \"multiple-choice\"\n            };\n            // Only add explanation if it has a value\n            const questionData = data.explanation && data.explanation.trim() !== '' ? {\n                ...baseQuestionData,\n                explanation: data.explanation\n            } : baseQuestionData;\n            console.log(\"Prepared question data:\", questionData);\n            // Convert base64 images to File objects for the API\n            console.log(\"Updating question with images using FormData\");\n            // Convert question image from base64 to File if present\n            let questionImageFile = null;\n            if (questionImage) {\n                try {\n                    const questionImageBlob = await fetch(questionImage).then((r)=>r.blob());\n                    questionImageFile = new File([\n                        questionImageBlob\n                    ], \"question-image.jpg\", {\n                        type: \"image/jpeg\"\n                    });\n                } catch (error) {\n                    console.warn(\"Failed to convert question image to file:\", error);\n                }\n            }\n            // Convert option images from base64 to File objects if present\n            const optionImageFiles = {};\n            for (const [key, imageData] of Object.entries(optionImages)){\n                if (imageData) {\n                    try {\n                        const optionImageBlob = await fetch(imageData).then((r)=>r.blob());\n                        optionImageFiles[key] = new File([\n                            optionImageBlob\n                        ], \"option-\".concat(key, \"-image.jpg\"), {\n                            type: \"image/jpeg\"\n                        });\n                    } catch (error) {\n                        console.warn(\"Failed to convert option \".concat(key, \" image to file:\"), error);\n                        optionImageFiles[key] = null;\n                    }\n                } else {\n                    optionImageFiles[key] = null;\n                }\n            }\n            // Submit to API using FormData\n            await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_15__.updateQuestionWithImages)(questionId, questionData, questionImageFile, optionImageFiles);\n            // Display success toast\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                title: \"Question Updated\",\n                description: \"Your question has been successfully updated.\"\n            });\n            // Redirect back to question bank\n            router.push(\"/admin/question-bank\");\n        } catch (error) {\n            console.error(\"Error updating question:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                title: \"Error\",\n                description: error.message || \"Failed to update question. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Cancel and go back\n    const handleCancel = ()=>{\n        router.push(\"/admin/question-bank\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                lineNumber: 406,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n            lineNumber: 405,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n            className: \"w-full max-w-4xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.Form, {\n                    ...form,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: form.handleSubmit(onSubmit),\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                        control: form.control,\n                                        name: \"subject\",\n                                        render: (param)=>{\n                                            let { field } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                        children: \"Subject *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                        onValueChange: handleSubjectChange,\n                                                        value: field.value,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                        placeholder: \"Select a subject\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                children: subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                        value: subject._id,\n                                                                        children: subject.name\n                                                                    }, subject._id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 433,\n                                                                        columnNumber: 29\n                                                                    }, void 0))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 431,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 21\n                                            }, void 0);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                        control: form.control,\n                                        name: \"topic\",\n                                        render: (param)=>{\n                                            let { field } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                        children: \"Topic *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                        onValueChange: field.onChange,\n                                                        value: field.value,\n                                                        disabled: topics.length === 0,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                        placeholder: topics.length === 0 ? \"Select a subject first\" : \"Select a topic\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                children: topics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                        value: topic._id,\n                                                                        children: topic.name\n                                                                    }, topic._id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 29\n                                                                    }, void 0))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 21\n                                            }, void 0);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"questionText\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: \"Question Text *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__.Textarea, {\n                                                    placeholder: \"Enter your question here...\",\n                                                    className: \"min-h-[100px]\",\n                                                    ...field\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                        children: \"Question Image (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                onClick: ()=>{\n                                                    var _questionImageRef_current;\n                                                    return (_questionImageRef_current = questionImageRef.current) === null || _questionImageRef_current === void 0 ? void 0 : _questionImageRef_current.click();\n                                                },\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Upload Image\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ref: questionImageRef,\n                                                type: \"file\",\n                                                accept: ACCEPTED_FILE_TYPES.join(\",\"),\n                                                onChange: (e)=>handleImageUpload(e, \"question\"),\n                                                className: \"hidden\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 19\n                                            }, this),\n                                            questionImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: questionImage,\n                                                        alt: \"Question\",\n                                                        width: 100,\n                                                        height: 100,\n                                                        className: \"rounded-md object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"destructive\",\n                                                        size: \"icon\",\n                                                        className: \"absolute -top-2 -right-2 h-6 w-6\",\n                                                        onClick: ()=>removeImage(\"question\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                        children: \"Answer Options *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            \"A\",\n                                            \"B\",\n                                            \"C\",\n                                            \"D\"\n                                        ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                        control: form.control,\n                                                        name: \"option\".concat(option),\n                                                        render: (param)=>{\n                                                            let { field } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                        children: [\n                                                                            \"Option \",\n                                                                            option,\n                                                                            optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-green-600 ml-2\",\n                                                                                children: \"(Image uploaded)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                                lineNumber: 546,\n                                                                                columnNumber: 33\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 543,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                            placeholder: optionImages[option] ? \"Option \".concat(option, \" text (optional - image uploaded)\") : \"Enter option \".concat(option, \" text or upload an image...\"),\n                                                                            ...field\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                            lineNumber: 550,\n                                                                            columnNumber: 31\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 549,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    optionValidationErrors[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-red-600\",\n                                                                        children: [\n                                                                            \"Option \",\n                                                                            option,\n                                                                            \" requires either text or an image\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 561,\n                                                                        columnNumber: 31\n                                                                    }, void 0)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 27\n                                                            }, void 0);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>{\n                                                                    var _optionImageRefs_option_current, _optionImageRefs_option;\n                                                                    return (_optionImageRefs_option = optionImageRefs[option]) === null || _optionImageRefs_option === void 0 ? void 0 : (_optionImageRefs_option_current = _optionImageRefs_option.current) === null || _optionImageRefs_option_current === void 0 ? void 0 : _optionImageRefs_option_current.click();\n                                                                },\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 578,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Image\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                ref: optionImageRefs[option],\n                                                                type: \"file\",\n                                                                accept: ACCEPTED_FILE_TYPES.join(\",\"),\n                                                                onChange: (e)=>handleImageUpload(e, option),\n                                                                className: \"hidden\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                        src: optionImages[option],\n                                                                        alt: \"Option \".concat(option),\n                                                                        width: 60,\n                                                                        height: 60,\n                                                                        className: \"rounded-md object-cover\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 590,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        type: \"button\",\n                                                                        variant: \"destructive\",\n                                                                        size: \"icon\",\n                                                                        className: \"absolute -top-1 -right-1 h-4 w-4\",\n                                                                        onClick: ()=>removeImage(option),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-2 w-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 597,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, option, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"correctAnswer\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: \"Correct Answer *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_9__.RadioGroup, {\n                                                    onValueChange: field.onChange,\n                                                    value: field.value,\n                                                    className: \"flex flex-row space-x-6\",\n                                                    children: [\n                                                        \"A\",\n                                                        \"B\",\n                                                        \"C\",\n                                                        \"D\"\n                                                    ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_9__.RadioGroupItem, {\n                                                                    value: option,\n                                                                    id: option\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 629,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                    htmlFor: option,\n                                                                    children: [\n                                                                        \"Option \",\n                                                                        option\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 630,\n                                                                    columnNumber: 29\n                                                                }, void 0)\n                                                            ]\n                                                        }, option, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                            lineNumber: 628,\n                                                            columnNumber: 27\n                                                        }, void 0))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 615,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"difficulty\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: \"Difficulty Level *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                onValueChange: field.onChange,\n                                                value: field.value,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                placeholder: \"Select difficulty level\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 650,\n                                                                columnNumber: 27\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 25\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"Easy\",\n                                                                children: \"Easy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"Medium\",\n                                                                children: \"Medium\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"Hard\",\n                                                                children: \"Hard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 656,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 641,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"explanation\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: [\n                                                    \"Explanation (Optional)\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.TooltipTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"inline h-4 w-4 ml-1 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 673,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.TooltipContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Provide an explanation for the correct answer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 677,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 676,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__.Textarea, {\n                                                    placeholder: \"Explain why this is the correct answer...\",\n                                                    className: \"min-h-[80px]\",\n                                                    ...field\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 688,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 669,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-4 pt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        onClick: handleCancel,\n                                        disabled: isSubmitting,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Updating...\"\n                                            ]\n                                        }, void 0, true) : \"Update Question\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 694,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                    lineNumber: 415,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                lineNumber: 414,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n            lineNumber: 413,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n        lineNumber: 412,\n        columnNumber: 5\n    }, this);\n}\n_s(EditQuestionForm, \"F/d5wKHUivNzAWSLkvfvZ9N8nyk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm\n    ];\n});\n_c = EditQuestionForm;\nvar _c;\n$RefreshReg$(_c, \"EditQuestionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/edit-question-form.tsx\n"));

/***/ })

});