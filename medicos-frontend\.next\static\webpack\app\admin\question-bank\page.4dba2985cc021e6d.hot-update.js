"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/admin/question-bank/pagination.tsx":
/*!***********************************************************!*\
  !*** ./src/components/admin/question-bank/pagination.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Pagination)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n\n\n\n\nfunction Pagination(param) {\n    let { currentPage, totalPages, onPageChange } = param;\n    // Generate page numbers to display\n    const getPageNumbers = ()=>{\n        const pages = [];\n        const maxPagesToShow = 5;\n        if (totalPages <= maxPagesToShow) {\n            // If we have fewer pages than the max to show, display all pages\n            for(let i = 1; i <= totalPages; i++){\n                pages.push(i);\n            }\n        } else {\n            // Always include first page\n            pages.push(1);\n            // Calculate start and end of page range\n            let start = Math.max(2, currentPage - 1);\n            let end = Math.min(totalPages - 1, currentPage + 1);\n            // Adjust if we're at the beginning or end\n            if (currentPage <= 2) {\n                end = Math.min(totalPages - 1, 4);\n            } else if (currentPage >= totalPages - 1) {\n                start = Math.max(2, totalPages - 3);\n            }\n            // Add ellipsis if needed\n            if (start > 2) {\n                pages.push(-1) // -1 represents ellipsis\n                ;\n            }\n            // Add middle pages\n            for(let i = start; i <= end; i++){\n                pages.push(i);\n            }\n            // Add ellipsis if needed\n            if (end < totalPages - 1) {\n                pages.push(-2) // -2 represents ellipsis\n                ;\n            }\n            // Always include last page\n            if (totalPages > 1) {\n                pages.push(totalPages);\n            }\n        }\n        return pages;\n    };\n    const pageNumbers = getPageNumbers();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center space-x-1 mt-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                variant: \"outline\",\n                size: \"icon\",\n                onClick: ()=>onPageChange(currentPage - 1),\n                disabled: currentPage === 1,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\pagination.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\pagination.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            pageNumbers.map((page, index)=>page < 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"ghost\",\n                    disabled: true,\n                    className: \"px-3\",\n                    children: \"...\"\n                }, \"ellipsis-\".concat(index), false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\pagination.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: currentPage === page ? \"default\" : \"outline\",\n                    onClick: ()=>onPageChange(page),\n                    className: \"px-3\",\n                    children: page\n                }, page, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\pagination.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 11\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                variant: \"outline\",\n                size: \"icon\",\n                onClick: ()=>onPageChange(currentPage + 1),\n                disabled: currentPage === totalPages,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\pagination.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\pagination.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\pagination.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n_c = Pagination;\nvar _c;\n$RefreshReg$(_c, \"Pagination\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/question-bank/pagination.tsx\n"));

/***/ })

});