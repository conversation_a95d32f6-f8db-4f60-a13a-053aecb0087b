"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/question-bank/question-bank.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionBank)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _question_list__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./question-list */ \"(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx\");\n/* harmony import */ var _components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/pagination */ \"(app-pages-browser)/./src/components/ui/pagination.tsx\");\n/* harmony import */ var _question_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./question-skeleton */ \"(app-pages-browser)/./src/components/admin/question-bank/question-skeleton.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction QuestionBank() {\n    _s();\n    // State for subjects and topics\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topics, setTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // State for filters\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_subjects\");\n    const [selectedTopic, setSelectedTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_topics\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // State for questions and pagination\n    const [questions, setQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch subjects with topics\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchSubjectsWithTopics = {\n                \"QuestionBank.useEffect.fetchSubjectsWithTopics\": async ()=>{\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        const response = await fetch(\"\".concat(baseUrl, \"/subjects/with-topics\"), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch subjects: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load subjects. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchSubjectsWithTopics\"];\n            fetchSubjectsWithTopics();\n        }\n    }[\"QuestionBank.useEffect\"], []);\n    // Update topics when subject changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            if (selectedSubject && selectedSubject !== \"all_subjects\") {\n                const selectedSubjectObj = subjects.find({\n                    \"QuestionBank.useEffect.selectedSubjectObj\": (s)=>s._id === selectedSubject\n                }[\"QuestionBank.useEffect.selectedSubjectObj\"]);\n                if (selectedSubjectObj && selectedSubjectObj.topics) {\n                    setTopics(selectedSubjectObj.topics);\n                } else {\n                    setTopics([]);\n                }\n                setSelectedTopic(\"all_topics\");\n            } else {\n                setTopics([]);\n            }\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        subjects\n    ]);\n    // Fetch questions with filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchQuestions = {\n                \"QuestionBank.useEffect.fetchQuestions\": async ()=>{\n                    setLoading(true);\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        // Build query parameters\n                        const params = new URLSearchParams();\n                        if (selectedSubject && selectedSubject !== \"all_subjects\") params.append('subjectId', selectedSubject);\n                        if (selectedTopic && selectedTopic !== \"all_topics\") params.append('topicId', selectedTopic);\n                        if (searchQuery) params.append('search', searchQuery);\n                        params.append('page', pagination.currentPage.toString());\n                        params.append('limit', pageSize.toString());\n                        const response = await fetch(\"\".concat(baseUrl, \"/questions?\").concat(params.toString()), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch questions: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setQuestions(data.questions);\n                        setPagination(data.pagination);\n                    } catch (error) {\n                        console.error(\"Error fetching questions:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load questions. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchQuestions\"];\n            fetchQuestions();\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        selectedTopic,\n        searchQuery,\n        pagination.currentPage,\n        pageSize\n    ]);\n    // Handle page change\n    const handlePageChange = (pageNumber)=>{\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: pageNumber\n            }));\n    };\n    // Handle difficulty change\n    const handleDifficultyChange = async (questionId, difficulty)=>{\n        try {\n            const baseUrl = \"http://localhost:3000/api\" || 0;\n            const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(questionId), {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                },\n                body: JSON.stringify({\n                    difficulty\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update question: \".concat(response.status));\n            }\n            // Update local state\n            setQuestions((prev)=>prev.map((q)=>q._id === questionId ? {\n                        ...q,\n                        difficulty\n                    } : q));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Question difficulty updated successfully\"\n            });\n        } catch (error) {\n            console.error(\"Error updating question difficulty:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update question difficulty\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Format questions for the QuestionList component\n    const formattedQuestions = questions.map((q)=>{\n        var _topics_find;\n        let parsedOptions = [];\n        if (q.options && q.options.length > 0) {\n            if (typeof q.options[0] === 'string') {\n                // Check if it's a single comma-separated string or an array of individual strings\n                if (q.options.length === 1 && q.options[0].includes(',')) {\n                    // Single comma-separated string: [\"Paris,London,Berlin,Madrid\"]\n                    const optionTexts = q.options[0].split(',');\n                    parsedOptions = optionTexts.map((text, index)=>({\n                            label: String.fromCharCode(97 + index),\n                            text: text.trim()\n                        }));\n                } else {\n                    // Array of individual strings: [\"Cerebrum\", \"Cerebellum\", \"Medulla\", \"Pons\"]\n                    parsedOptions = q.options.map((text, index)=>({\n                            label: String.fromCharCode(97 + index),\n                            text: text.trim()\n                        }));\n                }\n            } else {\n                // If options is already an array of objects\n                parsedOptions = q.options.map((opt, index)=>({\n                        label: String.fromCharCode(97 + index),\n                        text: typeof opt === 'string' ? opt : opt.text || '',\n                        imageUrl: typeof opt === 'object' ? opt.imageUrl : undefined\n                    }));\n            }\n        }\n        return {\n            id: q._id,\n            subject: q.subjectId.name,\n            topic: ((_topics_find = topics.find((t)=>t._id === q.topicId)) === null || _topics_find === void 0 ? void 0 : _topics_find.name) || \"Unknown\",\n            text: q.content,\n            options: parsedOptions,\n            difficulty: q.difficulty.charAt(0).toUpperCase() + q.difficulty.slice(1),\n            correctAnswer: q.answer,\n            reviewStatus: q.reviewStatus\n        };\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Subject\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedSubject,\n                                onValueChange: setSelectedSubject,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: \"Select Subject\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_subjects\",\n                                                children: \"All Subjects\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 15\n                                            }, this),\n                                            subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: subject._id,\n                                                    children: subject.name\n                                                }, subject._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Topic\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedTopic,\n                                onValueChange: setSelectedTopic,\n                                disabled: selectedSubject === \"all_subjects\" || topics.length === 0,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: selectedSubject !== \"all_subjects\" ? \"Select Topic\" : \"Select Subject First\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_topics\",\n                                                children: \"All Topics\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 15\n                                            }, this),\n                                            topics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: topic._id,\n                                                    children: topic.name\n                                                }, topic._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        type: \"text\",\n                                        placeholder: \"Search questions...\",\n                                        className: \"pl-8\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 274,\n                columnNumber: 9\n            }, this) : formattedQuestions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_list__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        questions: formattedQuestions,\n                        onDifficultyChange: handleDifficultyChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, this),\n                    pagination.totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__.Pagination, {\n                        currentPage: pagination.currentPage,\n                        totalPages: pagination.totalPages,\n                        onPageChange: handlePageChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"No questions found. Try adjusting your filters.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 293,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n        lineNumber: 216,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionBank, \"vuZocBv73CrThW8YbI7M7kNO/js=\");\n_c = QuestionBank;\nvar _c;\n$RefreshReg$(_c, \"QuestionBank\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx\n"));

/***/ })

});