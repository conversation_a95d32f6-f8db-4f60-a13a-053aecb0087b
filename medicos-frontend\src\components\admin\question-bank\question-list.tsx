import React from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Edit, Trash2 } from "lucide-react"
import { FormattedQuestion } from "@/types/question"
import { TextWithImages } from "@/components/ui/text-with-images"
import { Base64Image } from "@/components/ui/base64-image"

interface QuestionListProps {
  questions: FormattedQuestion[];
  onDifficultyChange: (questionId: string, difficulty: string) => void;
}

export default function QuestionList({ questions, onDifficultyChange }: QuestionListProps) {
  return (
    <div className="space-y-4">
      {questions.map((question) => (
        <Card key={question.id} className="overflow-hidden">
          <CardContent className="p-6">
            <div className="flex flex-col space-y-4">
              {/* Question header with metadata */}
              <div className="flex flex-wrap items-center justify-between gap-2">
                <div className="flex flex-wrap items-center gap-2">
                  <Badge variant="outline" className="font-normal">
                    {question.subject}
                  </Badge>
                  <Badge variant="outline" className="font-normal">
                    {question.topic}
                  </Badge>
                  
                  {question.reviewStatus && (
                    <Badge 
                      className={
                        question.reviewStatus === "approved" 
                          ? "bg-green-100 text-green-800 hover:bg-green-100" 
                          : question.reviewStatus === "rejected"
                          ? "bg-red-100 text-red-800 hover:bg-red-100"
                          : "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
                      }
                    >
                      {question.reviewStatus.charAt(0).toUpperCase() + question.reviewStatus.slice(1)}
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center gap-2">
                  <Select 
                    defaultValue={question.difficulty.toLowerCase()} 
                    onValueChange={(value) => onDifficultyChange(question.id, value)}
                  >
                    <SelectTrigger className="w-[110px]">
                      <SelectValue placeholder="Difficulty" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="easy">Easy</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="hard">Hard</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Button variant="ghost" size="icon">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              {/* Question text with image support */}
              <div className="font-medium">
                <TextWithImages
                  text={question.text}
                  maxImageWidth={400}
                  maxImageHeight={300}
                />
              </div>
              
              {/* Options */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {question.options.map((option, index) => (
                  <div 
                    key={index} 
                    className={`flex items-start p-3 rounded-md border ${
                      option.text === question.correctAnswer ? "border-green-500 bg-green-50" : "border-gray-200"
                    }`}
                  >
                    <div className="flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                      <span className="text-sm">{option.label}</span>
                    </div>
                    <div className="flex-1">
                      {option.text && !option.isImageOption && (
                        <div className="mb-2">
                          <TextWithImages
                            text={option.text}
                            maxImageWidth={200}
                            maxImageHeight={150}
                          />
                        </div>
                      )}
                      {option.imageUrl && (
                        <div className={option.isImageOption ? "" : "mt-2"}>
                          <Base64Image
                            src={option.imageUrl}
                            alt={`Option ${option.label}`}
                            maxWidth={200}
                            maxHeight={150}
                            className="border-0"
                          />
                        </div>
                      )}
                      {option.isImageOption && !option.imageUrl && (
                        <div className="text-gray-500 italic">Image option</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
