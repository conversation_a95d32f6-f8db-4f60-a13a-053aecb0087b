"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/edit-question/[id]/page",{

/***/ "(app-pages-browser)/./src/components/admin/edit-question-form.tsx":
/*!*****************************************************!*\
  !*** ./src/components/admin/edit-question-form.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditQuestionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/api/subjects */ \"(app-pages-browser)/./src/lib/api/subjects.ts\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/utils/imageUtils */ \"(app-pages-browser)/./src/utils/imageUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB\n;\nconst ACCEPTED_FILE_TYPES = [\n    \"image/jpeg\",\n    \"image/jpg\",\n    \"image/png\",\n    \"image/svg+xml\"\n];\n// Form schema with custom validation for options\nconst formSchema = zod__WEBPACK_IMPORTED_MODULE_17__.z.object({\n    subject: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a subject\"\n    }),\n    topic: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a topic\"\n    }),\n    questionText: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(5, {\n        message: \"Question must be at least 5 characters\"\n    }),\n    optionA: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionB: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionC: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionD: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    correctAnswer: zod__WEBPACK_IMPORTED_MODULE_17__.z.enum([\n        \"A\",\n        \"B\",\n        \"C\",\n        \"D\"\n    ], {\n        required_error: \"Please select the correct answer\"\n    }),\n    explanation: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    difficulty: zod__WEBPACK_IMPORTED_MODULE_17__.z.enum([\n        \"Easy\",\n        \"Medium\",\n        \"Hard\"\n    ], {\n        required_error: \"Please select a difficulty level\"\n    })\n});\nfunction EditQuestionForm(param) {\n    let { questionData, questionId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [questionImage, setQuestionImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [optionImages, setOptionImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: null,\n        B: null,\n        C: null,\n        D: null\n    });\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topics, setTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [optionValidationErrors, setOptionValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: false,\n        B: false,\n        C: false,\n        D: false\n    });\n    // Refs for file inputs\n    const questionImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const optionImageRefs = {\n        A: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        B: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        C: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        D: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null)\n    };\n    // Initialize form with question data\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(formSchema),\n        defaultValues: {\n            subject: \"\",\n            topic: \"\",\n            questionText: \"\",\n            optionA: \"\",\n            optionB: \"\",\n            optionC: \"\",\n            optionD: \"\",\n            correctAnswer: \"\",\n            explanation: \"\",\n            difficulty: \"\"\n        }\n    });\n    // Parse existing question data and populate form\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditQuestionForm.useEffect\": ()=>{\n            if (questionData && subjects.length > 0) {\n                var _questionData_options;\n                // Parse options from the question data\n                const parseOptions = {\n                    \"EditQuestionForm.useEffect.parseOptions\": ()=>{\n                        if (!questionData.options || questionData.options.length === 0) return [\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\"\n                        ];\n                        if (typeof questionData.options[0] === 'string') {\n                            if (questionData.options.length === 1 && questionData.options[0].includes(',')) {\n                                // Single comma-separated string\n                                const optionTexts = questionData.options[0].split(',');\n                                return optionTexts.map({\n                                    \"EditQuestionForm.useEffect.parseOptions\": (text)=>text.trim()\n                                }[\"EditQuestionForm.useEffect.parseOptions\"]).concat(Array(4 - optionTexts.length).fill(\"\")).slice(0, 4);\n                            } else {\n                                // Array of individual strings\n                                return questionData.options.concat(Array(4 - questionData.options.length).fill(\"\")).slice(0, 4);\n                            }\n                        }\n                        return [\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\"\n                        ];\n                    }\n                }[\"EditQuestionForm.useEffect.parseOptions\"];\n                const parsedOptions = parseOptions();\n                const newOptionImages = {\n                    A: null,\n                    B: null,\n                    C: null,\n                    D: null\n                };\n                // Check for base64 images in options and extract them\n                parsedOptions.forEach({\n                    \"EditQuestionForm.useEffect\": (option, index)=>{\n                        const optionKey = String.fromCharCode(65 + index); // A, B, C, D\n                        if (typeof option === 'string' && (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_16__.isBase64Image)(option)) {\n                            newOptionImages[optionKey] = (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_16__.ensureDataUrl)(option);\n                            parsedOptions[index] = \"\"; // Clear text since it's an image\n                        }\n                    }\n                }[\"EditQuestionForm.useEffect\"]);\n                setOptionImages(newOptionImages);\n                var _questionData_options_findIndex;\n                // Find correct answer letter\n                const answerIndex = (_questionData_options_findIndex = (_questionData_options = questionData.options) === null || _questionData_options === void 0 ? void 0 : _questionData_options.findIndex({\n                    \"EditQuestionForm.useEffect\": (opt)=>opt === questionData.answer\n                }[\"EditQuestionForm.useEffect\"])) !== null && _questionData_options_findIndex !== void 0 ? _questionData_options_findIndex : -1;\n                const correctAnswerLetter = answerIndex >= 0 ? String.fromCharCode(65 + answerIndex) : \"A\";\n                // Handle topicId - it could be a string ID or a populated object\n                const topicId = questionData.topicId ? typeof questionData.topicId === 'string' ? questionData.topicId : questionData.topicId._id : \"\";\n                // Set form values\n                form.reset({\n                    subject: questionData.subjectId._id,\n                    topic: topicId,\n                    questionText: questionData.content,\n                    optionA: parsedOptions[0] || \"\",\n                    optionB: parsedOptions[1] || \"\",\n                    optionC: parsedOptions[2] || \"\",\n                    optionD: parsedOptions[3] || \"\",\n                    correctAnswer: correctAnswerLetter,\n                    explanation: questionData.explanation || \"\",\n                    difficulty: questionData.difficulty.charAt(0).toUpperCase() + questionData.difficulty.slice(1)\n                });\n                // Set topics for the selected subject\n                const selectedSubject = subjects.find({\n                    \"EditQuestionForm.useEffect.selectedSubject\": (s)=>s._id === questionData.subjectId._id\n                }[\"EditQuestionForm.useEffect.selectedSubject\"]);\n                if (selectedSubject) {\n                    setTopics(selectedSubject.topics || []);\n                }\n            }\n        }\n    }[\"EditQuestionForm.useEffect\"], [\n        questionData,\n        subjects,\n        form\n    ]);\n    // Fetch subjects and topics from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditQuestionForm.useEffect\": ()=>{\n            const fetchSubjectsAndTopics = {\n                \"EditQuestionForm.useEffect.fetchSubjectsAndTopics\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const data = await (0,_lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__.getSubjectsWithTopics)();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects and topics:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                            title: \"Error\",\n                            description: error.message || \"Failed to load subjects and topics\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"EditQuestionForm.useEffect.fetchSubjectsAndTopics\"];\n            fetchSubjectsAndTopics();\n        }\n    }[\"EditQuestionForm.useEffect\"], []);\n    // Handle subject change to update topics\n    const handleSubjectChange = (value)=>{\n        form.setValue(\"subject\", value);\n        form.setValue(\"topic\", \"\");\n        // Find the selected subject and set its topics\n        const selectedSubject = subjects.find((subject)=>subject._id === value);\n        if (selectedSubject) {\n            setTopics(selectedSubject.topics || []);\n        } else {\n            setTopics([]);\n        }\n    };\n    // Handle image upload\n    const handleImageUpload = (e, type)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            if (type === \"question\") {\n                var _event_target;\n                setQuestionImage((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result);\n            } else {\n                setOptionImages((prev)=>{\n                    var _event_target;\n                    return {\n                        ...prev,\n                        [type]: (_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result\n                    };\n                });\n                // Clear validation error for this option\n                setOptionValidationErrors((prev)=>({\n                        ...prev,\n                        [type]: false\n                    }));\n            }\n        };\n        reader.readAsDataURL(file);\n    };\n    // Remove image\n    const removeImage = (type)=>{\n        if (type === \"question\") {\n            setQuestionImage(null);\n            if (questionImageRef.current) {\n                questionImageRef.current.value = \"\";\n            }\n        } else {\n            var _optionImageRefs_type;\n            setOptionImages((prev)=>({\n                    ...prev,\n                    [type]: null\n                }));\n            if ((_optionImageRefs_type = optionImageRefs[type]) === null || _optionImageRefs_type === void 0 ? void 0 : _optionImageRefs_type.current) {\n                optionImageRefs[type].current.value = \"\";\n            }\n            // Clear form validation error for this option if it exists\n            form.clearErrors(\"option\".concat(type));\n            // Update validation state\n            setOptionValidationErrors((prev)=>({\n                    ...prev,\n                    [type]: false\n                }));\n        }\n    };\n    // Custom validation function\n    const validateOptions = (formData)=>{\n        const errors = [];\n        const validationState = {};\n        const options = [\n            'A',\n            'B',\n            'C',\n            'D'\n        ];\n        for (const option of options){\n            const hasText = formData[\"option\".concat(option)] && formData[\"option\".concat(option)].trim() !== '';\n            const hasImage = optionImages[option] !== null;\n            if (!hasText && !hasImage) {\n                errors.push(\"Option \".concat(option, \" must have either text or an image\"));\n                validationState[option] = true;\n            } else {\n                validationState[option] = false;\n            }\n        }\n        // Update validation state\n        setOptionValidationErrors(validationState);\n        return errors;\n    };\n    // Handle form submission\n    const onSubmit = async (data)=>{\n        setIsSubmitting(true);\n        try {\n            var _data_optionA, _data_optionB, _data_optionC, _data_optionD;\n            console.log(\"Form data:\", data);\n            // Validate that each option has either text or image\n            const validationErrors = validateOptions(data);\n            if (validationErrors.length > 0) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                    title: \"Validation Error\",\n                    description: validationErrors.join(', '),\n                    variant: \"destructive\"\n                });\n                setIsSubmitting(false);\n                return;\n            }\n            // Convert option data to array format expected by API\n            // Use image base64 if no text, otherwise use text\n            const options = [\n                ((_data_optionA = data.optionA) === null || _data_optionA === void 0 ? void 0 : _data_optionA.trim()) || optionImages.A || '',\n                ((_data_optionB = data.optionB) === null || _data_optionB === void 0 ? void 0 : _data_optionB.trim()) || optionImages.B || '',\n                ((_data_optionC = data.optionC) === null || _data_optionC === void 0 ? void 0 : _data_optionC.trim()) || optionImages.C || '',\n                ((_data_optionD = data.optionD) === null || _data_optionD === void 0 ? void 0 : _data_optionD.trim()) || optionImages.D || ''\n            ];\n            // Map correctAnswer (A, B, C, D) to the actual option value\n            const answerMap = {\n                A: 0,\n                B: 1,\n                C: 2,\n                D: 3\n            };\n            const answerIndex = answerMap[data.correctAnswer];\n            const answer = options[answerIndex];\n            // Convert difficulty to lowercase to match API expectations\n            const difficulty = data.difficulty.toLowerCase();\n            // Create base question data\n            const baseQuestionData = {\n                content: data.questionText,\n                options,\n                answer,\n                subjectId: data.subject,\n                topicId: data.topic,\n                difficulty,\n                type: \"multiple-choice\"\n            };\n            // Only add explanation if it has a value\n            const questionData = data.explanation && data.explanation.trim() !== '' ? {\n                ...baseQuestionData,\n                explanation: data.explanation\n            } : baseQuestionData;\n            console.log(\"Prepared question data:\", questionData);\n            // For now, we'll convert all images to base64 and include them in the options\n            // This matches the format expected by the question bank display\n            console.log(\"Updating question with base64 images embedded in options\");\n            let finalQuestionData = {\n                ...questionData\n            };\n            // If question has an image, embed it in the question text as base64\n            if (questionImage) {\n                finalQuestionData.content = \"\".concat(questionData.content, \"\\n\").concat(questionImage);\n            }\n            // Submit to API - the options already contain base64 images where needed\n            await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_15__.updateQuestion)(questionId, finalQuestionData);\n            // Display success toast\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                title: \"Question Updated\",\n                description: \"Your question has been successfully updated.\"\n            });\n            // Redirect back to question bank\n            router.push(\"/admin/question-bank\");\n        } catch (error) {\n            console.error(\"Error updating question:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                title: \"Error\",\n                description: error.message || \"Failed to update question. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Cancel and go back\n    const handleCancel = ()=>{\n        router.push(\"/admin/question-bank\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                lineNumber: 387,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n            lineNumber: 386,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n            className: \"w-full max-w-4xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.Form, {\n                    ...form,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: form.handleSubmit(onSubmit),\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                        control: form.control,\n                                        name: \"subject\",\n                                        render: (param)=>{\n                                            let { field } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                        children: \"Subject *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                        onValueChange: handleSubjectChange,\n                                                        value: field.value,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                        placeholder: \"Select a subject\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                children: subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                        value: subject._id,\n                                                                        children: subject.name\n                                                                    }, subject._id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 29\n                                                                    }, void 0))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 21\n                                            }, void 0);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                        control: form.control,\n                                        name: \"topic\",\n                                        render: (param)=>{\n                                            let { field } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                        children: \"Topic *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                        onValueChange: field.onChange,\n                                                        value: field.value,\n                                                        disabled: topics.length === 0,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                        placeholder: topics.length === 0 ? \"Select a subject first\" : \"Select a topic\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                children: topics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                        value: topic._id,\n                                                                        children: topic.name\n                                                                    }, topic._id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 439,\n                                                                        columnNumber: 29\n                                                                    }, void 0))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 21\n                                            }, void 0);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"questionText\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: \"Question Text *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__.Textarea, {\n                                                    placeholder: \"Enter your question here...\",\n                                                    className: \"min-h-[100px]\",\n                                                    ...field\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                        children: \"Question Image (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                onClick: ()=>{\n                                                    var _questionImageRef_current;\n                                                    return (_questionImageRef_current = questionImageRef.current) === null || _questionImageRef_current === void 0 ? void 0 : _questionImageRef_current.click();\n                                                },\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Upload Image\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ref: questionImageRef,\n                                                type: \"file\",\n                                                accept: ACCEPTED_FILE_TYPES.join(\",\"),\n                                                onChange: (e)=>handleImageUpload(e, \"question\"),\n                                                className: \"hidden\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 19\n                                            }, this),\n                                            questionImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: questionImage,\n                                                        alt: \"Question\",\n                                                        width: 100,\n                                                        height: 100,\n                                                        className: \"rounded-md object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"destructive\",\n                                                        size: \"icon\",\n                                                        className: \"absolute -top-2 -right-2 h-6 w-6\",\n                                                        onClick: ()=>removeImage(\"question\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                        children: \"Answer Options *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            \"A\",\n                                            \"B\",\n                                            \"C\",\n                                            \"D\"\n                                        ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                        control: form.control,\n                                                        name: \"option\".concat(option),\n                                                        render: (param)=>{\n                                                            let { field } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                        children: [\n                                                                            \"Option \",\n                                                                            option,\n                                                                            optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-green-600 ml-2\",\n                                                                                children: \"(Image uploaded)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                                lineNumber: 527,\n                                                                                columnNumber: 33\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 524,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                            placeholder: optionImages[option] ? \"Option \".concat(option, \" text (optional - image uploaded)\") : \"Enter option \".concat(option, \" text or upload an image...\"),\n                                                                            ...field\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                            lineNumber: 531,\n                                                                            columnNumber: 31\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 530,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 540,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    optionValidationErrors[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-red-600\",\n                                                                        children: [\n                                                                            \"Option \",\n                                                                            option,\n                                                                            \" requires either text or an image\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 542,\n                                                                        columnNumber: 31\n                                                                    }, void 0)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 27\n                                                            }, void 0);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>{\n                                                                    var _optionImageRefs_option_current, _optionImageRefs_option;\n                                                                    return (_optionImageRefs_option = optionImageRefs[option]) === null || _optionImageRefs_option === void 0 ? void 0 : (_optionImageRefs_option_current = _optionImageRefs_option.current) === null || _optionImageRefs_option_current === void 0 ? void 0 : _optionImageRefs_option_current.click();\n                                                                },\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Image\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 552,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                ref: optionImageRefs[option],\n                                                                type: \"file\",\n                                                                accept: ACCEPTED_FILE_TYPES.join(\",\"),\n                                                                onChange: (e)=>handleImageUpload(e, option),\n                                                                className: \"hidden\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                        src: optionImages[option],\n                                                                        alt: \"Option \".concat(option),\n                                                                        width: 60,\n                                                                        height: 60,\n                                                                        className: \"rounded-md object-cover\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 571,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        type: \"button\",\n                                                                        variant: \"destructive\",\n                                                                        size: \"icon\",\n                                                                        className: \"absolute -top-1 -right-1 h-4 w-4\",\n                                                                        onClick: ()=>removeImage(option),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-2 w-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                            lineNumber: 585,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 578,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, option, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"correctAnswer\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: \"Correct Answer *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_9__.RadioGroup, {\n                                                    onValueChange: field.onChange,\n                                                    value: field.value,\n                                                    className: \"flex flex-row space-x-6\",\n                                                    children: [\n                                                        \"A\",\n                                                        \"B\",\n                                                        \"C\",\n                                                        \"D\"\n                                                    ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_9__.RadioGroupItem, {\n                                                                    value: option,\n                                                                    id: option\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 610,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                    htmlFor: option,\n                                                                    children: [\n                                                                        \"Option \",\n                                                                        option\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 611,\n                                                                    columnNumber: 29\n                                                                }, void 0)\n                                                            ]\n                                                        }, option, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 27\n                                                        }, void 0))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 596,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"difficulty\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: \"Difficulty Level *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                onValueChange: field.onChange,\n                                                value: field.value,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                placeholder: \"Select difficulty level\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 631,\n                                                                columnNumber: 27\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 25\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"Easy\",\n                                                                children: \"Easy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"Medium\",\n                                                                children: \"Medium\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"Hard\",\n                                                                children: \"Hard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 637,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"explanation\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: [\n                                                    \"Explanation (Optional)\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.TooltipTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"inline h-4 w-4 ml-1 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 655,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.TooltipContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Provide an explanation for the correct answer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 657,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__.Textarea, {\n                                                    placeholder: \"Explain why this is the correct answer...\",\n                                                    className: \"min-h-[80px]\",\n                                                    ...field\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-4 pt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        onClick: handleCancel,\n                                        disabled: isSubmitting,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 687,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Updating...\"\n                                            ]\n                                        }, void 0, true) : \"Update Question\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 675,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                lineNumber: 395,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n            lineNumber: 394,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n        lineNumber: 393,\n        columnNumber: 5\n    }, this);\n}\n_s(EditQuestionForm, \"F/d5wKHUivNzAWSLkvfvZ9N8nyk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm\n    ];\n});\n_c = EditQuestionForm;\nvar _c;\n$RefreshReg$(_c, \"EditQuestionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/edit-question-form.tsx\n"));

/***/ })

});