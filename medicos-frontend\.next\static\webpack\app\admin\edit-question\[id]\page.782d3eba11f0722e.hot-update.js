"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/edit-question/[id]/page",{

/***/ "(app-pages-browser)/./src/components/admin/edit-question-form.tsx":
/*!*****************************************************!*\
  !*** ./src/components/admin/edit-question-form.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditQuestionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/api/subjects */ \"(app-pages-browser)/./src/lib/api/subjects.ts\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/utils/imageUtils */ \"(app-pages-browser)/./src/utils/imageUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB\n;\nconst ACCEPTED_FILE_TYPES = [\n    \"image/jpeg\",\n    \"image/jpg\",\n    \"image/png\",\n    \"image/svg+xml\"\n];\n// Form schema with custom validation for options\nconst formSchema = zod__WEBPACK_IMPORTED_MODULE_17__.z.object({\n    subject: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a subject\"\n    }),\n    topic: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a topic\"\n    }),\n    questionText: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(5, {\n        message: \"Question must be at least 5 characters\"\n    }),\n    optionA: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionB: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionC: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionD: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    correctAnswer: zod__WEBPACK_IMPORTED_MODULE_17__.z.enum([\n        \"A\",\n        \"B\",\n        \"C\",\n        \"D\"\n    ], {\n        required_error: \"Please select the correct answer\"\n    }),\n    explanation: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    difficulty: zod__WEBPACK_IMPORTED_MODULE_17__.z.enum([\n        \"Easy\",\n        \"Medium\",\n        \"Hard\"\n    ], {\n        required_error: \"Please select a difficulty level\"\n    })\n});\nfunction EditQuestionForm(param) {\n    let { questionData, questionId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [questionImage, setQuestionImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [optionImages, setOptionImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: null,\n        B: null,\n        C: null,\n        D: null\n    });\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topics, setTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [optionValidationErrors, setOptionValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: false,\n        B: false,\n        C: false,\n        D: false\n    });\n    // Refs for file inputs\n    const questionImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const optionImageRefs = {\n        A: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        B: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        C: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        D: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null)\n    };\n    // Initialize form with question data\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(formSchema),\n        defaultValues: {\n            subject: \"\",\n            topic: \"\",\n            questionText: \"\",\n            optionA: \"\",\n            optionB: \"\",\n            optionC: \"\",\n            optionD: \"\",\n            correctAnswer: \"\",\n            explanation: \"\",\n            difficulty: \"\"\n        }\n    });\n    // Parse existing question data and populate form\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditQuestionForm.useEffect\": ()=>{\n            if (questionData && subjects.length > 0) {\n                var _questionData_options;\n                // Parse options from the question data\n                const parseOptions = {\n                    \"EditQuestionForm.useEffect.parseOptions\": ()=>{\n                        if (!questionData.options || questionData.options.length === 0) return [\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\"\n                        ];\n                        if (typeof questionData.options[0] === 'string') {\n                            if (questionData.options.length === 1 && questionData.options[0].includes(',')) {\n                                // Single comma-separated string\n                                const optionTexts = questionData.options[0].split(',');\n                                return optionTexts.map({\n                                    \"EditQuestionForm.useEffect.parseOptions\": (text)=>text.trim()\n                                }[\"EditQuestionForm.useEffect.parseOptions\"]).concat(Array(4 - optionTexts.length).fill(\"\")).slice(0, 4);\n                            } else {\n                                // Array of individual strings\n                                return questionData.options.concat(Array(4 - questionData.options.length).fill(\"\")).slice(0, 4);\n                            }\n                        }\n                        return [\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\"\n                        ];\n                    }\n                }[\"EditQuestionForm.useEffect.parseOptions\"];\n                const parsedOptions = parseOptions();\n                const newOptionImages = {\n                    A: null,\n                    B: null,\n                    C: null,\n                    D: null\n                };\n                // Check for base64 images in options and extract them\n                parsedOptions.forEach({\n                    \"EditQuestionForm.useEffect\": (option, index)=>{\n                        const optionKey = String.fromCharCode(65 + index); // A, B, C, D\n                        if (typeof option === 'string' && (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_16__.isBase64Image)(option)) {\n                            newOptionImages[optionKey] = (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_16__.ensureDataUrl)(option);\n                            parsedOptions[index] = \"\"; // Clear text since it's an image\n                        }\n                    }\n                }[\"EditQuestionForm.useEffect\"]);\n                setOptionImages(newOptionImages);\n                var _questionData_options_findIndex;\n                // Find correct answer letter\n                const answerIndex = (_questionData_options_findIndex = (_questionData_options = questionData.options) === null || _questionData_options === void 0 ? void 0 : _questionData_options.findIndex({\n                    \"EditQuestionForm.useEffect\": (opt)=>opt === questionData.answer\n                }[\"EditQuestionForm.useEffect\"])) !== null && _questionData_options_findIndex !== void 0 ? _questionData_options_findIndex : -1;\n                const correctAnswerLetter = answerIndex >= 0 ? String.fromCharCode(65 + answerIndex) : \"A\";\n                // Handle topicId - it could be a string ID or a populated object\n                const topicId = questionData.topicId ? typeof questionData.topicId === 'string' ? questionData.topicId : questionData.topicId._id : \"\";\n                // Set form values\n                form.reset({\n                    subject: questionData.subjectId._id,\n                    topic: topicId,\n                    questionText: questionData.content,\n                    optionA: parsedOptions[0] || \"\",\n                    optionB: parsedOptions[1] || \"\",\n                    optionC: parsedOptions[2] || \"\",\n                    optionD: parsedOptions[3] || \"\",\n                    correctAnswer: correctAnswerLetter,\n                    explanation: questionData.explanation || \"\",\n                    difficulty: questionData.difficulty.charAt(0).toUpperCase() + questionData.difficulty.slice(1)\n                });\n                // Set topics for the selected subject\n                const selectedSubject = subjects.find({\n                    \"EditQuestionForm.useEffect.selectedSubject\": (s)=>s._id === questionData.subjectId._id\n                }[\"EditQuestionForm.useEffect.selectedSubject\"]);\n                if (selectedSubject) {\n                    setTopics(selectedSubject.topics || []);\n                }\n            }\n        }\n    }[\"EditQuestionForm.useEffect\"], [\n        questionData,\n        subjects,\n        form\n    ]);\n    // Fetch subjects and topics from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditQuestionForm.useEffect\": ()=>{\n            const fetchSubjectsAndTopics = {\n                \"EditQuestionForm.useEffect.fetchSubjectsAndTopics\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const data = await (0,_lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__.getSubjectsWithTopics)();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects and topics:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                            title: \"Error\",\n                            description: error.message || \"Failed to load subjects and topics\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"EditQuestionForm.useEffect.fetchSubjectsAndTopics\"];\n            fetchSubjectsAndTopics();\n        }\n    }[\"EditQuestionForm.useEffect\"], []);\n    // Handle subject change to update topics\n    const handleSubjectChange = (value)=>{\n        form.setValue(\"subject\", value);\n        form.setValue(\"topic\", \"\");\n        // Find the selected subject and set its topics\n        const selectedSubject = subjects.find((subject)=>subject._id === value);\n        if (selectedSubject) {\n            setTopics(selectedSubject.topics || []);\n        } else {\n            setTopics([]);\n        }\n    };\n    // Handle image upload\n    const handleImageUpload = (e, type)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            if (type === \"question\") {\n                var _event_target;\n                setQuestionImage((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result);\n            } else {\n                setOptionImages((prev)=>{\n                    var _event_target;\n                    return {\n                        ...prev,\n                        [type]: (_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result\n                    };\n                });\n                // Clear validation error for this option\n                setOptionValidationErrors((prev)=>({\n                        ...prev,\n                        [type]: false\n                    }));\n            }\n        };\n        reader.readAsDataURL(file);\n    };\n    // Remove image\n    const removeImage = (type)=>{\n        if (type === \"question\") {\n            setQuestionImage(null);\n            if (questionImageRef.current) {\n                questionImageRef.current.value = \"\";\n            }\n        } else {\n            var _optionImageRefs_type;\n            setOptionImages((prev)=>({\n                    ...prev,\n                    [type]: null\n                }));\n            if ((_optionImageRefs_type = optionImageRefs[type]) === null || _optionImageRefs_type === void 0 ? void 0 : _optionImageRefs_type.current) {\n                optionImageRefs[type].current.value = \"\";\n            }\n            // Clear form validation error for this option if it exists\n            form.clearErrors(\"option\".concat(type));\n            // Update validation state\n            setOptionValidationErrors((prev)=>({\n                    ...prev,\n                    [type]: false\n                }));\n        }\n    };\n    // Custom validation function\n    const validateOptions = (formData)=>{\n        const errors = [];\n        const validationState = {};\n        const options = [\n            'A',\n            'B',\n            'C',\n            'D'\n        ];\n        for (const option of options){\n            const hasText = formData[\"option\".concat(option)] && formData[\"option\".concat(option)].trim() !== '';\n            const hasImage = optionImages[option] !== null;\n            if (!hasText && !hasImage) {\n                errors.push(\"Option \".concat(option, \" must have either text or an image\"));\n                validationState[option] = true;\n            } else {\n                validationState[option] = false;\n            }\n        }\n        // Update validation state\n        setOptionValidationErrors(validationState);\n        return errors;\n    };\n    // Handle form submission\n    const onSubmit = async (data)=>{\n        setIsSubmitting(true);\n        try {\n            var _data_optionA, _data_optionB, _data_optionC, _data_optionD;\n            console.log(\"Form data:\", data);\n            // Validate that each option has either text or image\n            const validationErrors = validateOptions(data);\n            if (validationErrors.length > 0) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                    title: \"Validation Error\",\n                    description: validationErrors.join(', '),\n                    variant: \"destructive\"\n                });\n                setIsSubmitting(false);\n                return;\n            }\n            // Convert option data to array format expected by API\n            // Use image base64 if no text, otherwise use text\n            const options = [\n                ((_data_optionA = data.optionA) === null || _data_optionA === void 0 ? void 0 : _data_optionA.trim()) || optionImages.A || '',\n                ((_data_optionB = data.optionB) === null || _data_optionB === void 0 ? void 0 : _data_optionB.trim()) || optionImages.B || '',\n                ((_data_optionC = data.optionC) === null || _data_optionC === void 0 ? void 0 : _data_optionC.trim()) || optionImages.C || '',\n                ((_data_optionD = data.optionD) === null || _data_optionD === void 0 ? void 0 : _data_optionD.trim()) || optionImages.D || ''\n            ];\n            // Map correctAnswer (A, B, C, D) to the actual option value\n            const answerMap = {\n                A: 0,\n                B: 1,\n                C: 2,\n                D: 3\n            };\n            const answerIndex = answerMap[data.correctAnswer];\n            const answer = options[answerIndex];\n            // Convert difficulty to lowercase to match API expectations\n            const difficulty = data.difficulty.toLowerCase();\n            // Create base question data\n            const baseQuestionData = {\n                content: data.questionText,\n                options,\n                answer,\n                subjectId: data.subject,\n                topicId: data.topic,\n                difficulty,\n                type: \"multiple-choice\"\n            };\n            // Only add explanation if it has a value\n            const questionData = data.explanation && data.explanation.trim() !== '' ? {\n                ...baseQuestionData,\n                explanation: data.explanation\n            } : baseQuestionData;\n            console.log(\"Prepared question data:\", questionData);\n            // The backend expects images embedded as base64 in options, not as separate files\n            console.log(\"Updating question with embedded base64 images\");\n            let finalQuestionData = {\n                ...questionData\n            };\n            // If question has an image, embed it in the question text as base64\n            if (questionImage) {\n                finalQuestionData.content = \"\".concat(questionData.content, \"\\n\").concat(questionImage);\n            }\n            // Submit to API using JSON (the options already contain base64 images where needed)\n            await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_15__.updateQuestion)(questionId, finalQuestionData);\n            // Display success toast\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                title: \"Question Updated\",\n                description: \"Your question has been successfully updated.\"\n            });\n            // Redirect back to question bank\n            router.push(\"/admin/question-bank\");\n        } catch (error) {\n            console.error(\"Error updating question:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                title: \"Error\",\n                description: error.message || \"Failed to update question. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Cancel and go back\n    const handleCancel = ()=>{\n        router.push(\"/admin/question-bank\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                lineNumber: 386,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n            lineNumber: 385,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n            className: \"w-full max-w-4xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.Form, {\n                    ...form,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: form.handleSubmit(onSubmit),\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                        control: form.control,\n                                        name: \"subject\",\n                                        render: (param)=>{\n                                            let { field } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                        children: \"Subject *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                        onValueChange: handleSubjectChange,\n                                                        value: field.value,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                        placeholder: \"Select a subject\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 408,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                children: subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                        value: subject._id,\n                                                                        children: subject.name\n                                                                    }, subject._id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 29\n                                                                    }, void 0))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 21\n                                            }, void 0);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                        control: form.control,\n                                        name: \"topic\",\n                                        render: (param)=>{\n                                            let { field } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                        children: \"Topic *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                        onValueChange: field.onChange,\n                                                        value: field.value,\n                                                        disabled: topics.length === 0,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                        placeholder: topics.length === 0 ? \"Select a subject first\" : \"Select a topic\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 433,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 431,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                children: topics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                        value: topic._id,\n                                                                        children: topic.name\n                                                                    }, topic._id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 438,\n                                                                        columnNumber: 29\n                                                                    }, void 0))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 21\n                                            }, void 0);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"questionText\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: \"Question Text *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__.Textarea, {\n                                                    placeholder: \"Enter your question here...\",\n                                                    className: \"min-h-[100px]\",\n                                                    ...field\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                        children: \"Question Image (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                onClick: ()=>{\n                                                    var _questionImageRef_current;\n                                                    return (_questionImageRef_current = questionImageRef.current) === null || _questionImageRef_current === void 0 ? void 0 : _questionImageRef_current.click();\n                                                },\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Upload Image\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ref: questionImageRef,\n                                                type: \"file\",\n                                                accept: ACCEPTED_FILE_TYPES.join(\",\"),\n                                                onChange: (e)=>handleImageUpload(e, \"question\"),\n                                                className: \"hidden\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 19\n                                            }, this),\n                                            questionImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: questionImage,\n                                                        alt: \"Question\",\n                                                        width: 100,\n                                                        height: 100,\n                                                        className: \"rounded-md object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"destructive\",\n                                                        size: \"icon\",\n                                                        className: \"absolute -top-2 -right-2 h-6 w-6\",\n                                                        onClick: ()=>removeImage(\"question\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                        children: \"Answer Options *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            \"A\",\n                                            \"B\",\n                                            \"C\",\n                                            \"D\"\n                                        ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                        control: form.control,\n                                                        name: \"option\".concat(option),\n                                                        render: (param)=>{\n                                                            let { field } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                        children: [\n                                                                            \"Option \",\n                                                                            option,\n                                                                            optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-green-600 ml-2\",\n                                                                                children: \"(Image uploaded)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                                lineNumber: 526,\n                                                                                columnNumber: 33\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 523,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                            placeholder: optionImages[option] ? \"Option \".concat(option, \" text (optional - image uploaded)\") : \"Enter option \".concat(option, \" text or upload an image...\"),\n                                                                            ...field\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 31\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 529,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 539,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    optionValidationErrors[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-red-600\",\n                                                                        children: [\n                                                                            \"Option \",\n                                                                            option,\n                                                                            \" requires either text or an image\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 541,\n                                                                        columnNumber: 31\n                                                                    }, void 0)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 27\n                                                            }, void 0);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>{\n                                                                    var _optionImageRefs_option_current, _optionImageRefs_option;\n                                                                    return (_optionImageRefs_option = optionImageRefs[option]) === null || _optionImageRefs_option === void 0 ? void 0 : (_optionImageRefs_option_current = _optionImageRefs_option.current) === null || _optionImageRefs_option_current === void 0 ? void 0 : _optionImageRefs_option_current.click();\n                                                                },\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Image\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                ref: optionImageRefs[option],\n                                                                type: \"file\",\n                                                                accept: ACCEPTED_FILE_TYPES.join(\",\"),\n                                                                onChange: (e)=>handleImageUpload(e, option),\n                                                                className: \"hidden\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                        src: optionImages[option],\n                                                                        alt: \"Option \".concat(option),\n                                                                        width: 60,\n                                                                        height: 60,\n                                                                        className: \"rounded-md object-cover\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 570,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        type: \"button\",\n                                                                        variant: \"destructive\",\n                                                                        size: \"icon\",\n                                                                        className: \"absolute -top-1 -right-1 h-4 w-4\",\n                                                                        onClick: ()=>removeImage(option),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-2 w-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                            lineNumber: 584,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 577,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, option, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"correctAnswer\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: \"Correct Answer *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_9__.RadioGroup, {\n                                                    onValueChange: field.onChange,\n                                                    value: field.value,\n                                                    className: \"flex flex-row space-x-6\",\n                                                    children: [\n                                                        \"A\",\n                                                        \"B\",\n                                                        \"C\",\n                                                        \"D\"\n                                                    ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_9__.RadioGroupItem, {\n                                                                    value: option,\n                                                                    id: option\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 609,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                    htmlFor: option,\n                                                                    children: [\n                                                                        \"Option \",\n                                                                        option\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 610,\n                                                                    columnNumber: 29\n                                                                }, void 0)\n                                                            ]\n                                                        }, option, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 27\n                                                        }, void 0))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"difficulty\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: \"Difficulty Level *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                onValueChange: field.onChange,\n                                                value: field.value,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                placeholder: \"Select difficulty level\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 630,\n                                                                columnNumber: 27\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 25\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"Easy\",\n                                                                children: \"Easy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"Medium\",\n                                                                children: \"Medium\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"Hard\",\n                                                                children: \"Hard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"explanation\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: [\n                                                    \"Explanation (Optional)\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.TooltipTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"inline h-4 w-4 ml-1 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 654,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.TooltipContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Provide an explanation for the correct answer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 656,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__.Textarea, {\n                                                    placeholder: \"Explain why this is the correct answer...\",\n                                                    className: \"min-h-[80px]\",\n                                                    ...field\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 645,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-4 pt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        onClick: handleCancel,\n                                        disabled: isSubmitting,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Updating...\"\n                                            ]\n                                        }, void 0, true) : \"Update Question\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 674,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                lineNumber: 394,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n            lineNumber: 393,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n        lineNumber: 392,\n        columnNumber: 5\n    }, this);\n}\n_s(EditQuestionForm, \"F/d5wKHUivNzAWSLkvfvZ9N8nyk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm\n    ];\n});\n_c = EditQuestionForm;\nvar _c;\n$RefreshReg$(_c, \"EditQuestionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/edit-question-form.tsx\n"));

/***/ })

});