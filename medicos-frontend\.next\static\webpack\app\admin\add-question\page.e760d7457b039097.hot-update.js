"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/add-question/page",{

/***/ "(app-pages-browser)/./src/lib/api/questions.ts":
/*!**********************************!*\
  !*** ./src/lib/api/questions.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createQuestion: () => (/* binding */ createQuestion),\n/* harmony export */   createQuestionWithImages: () => (/* binding */ createQuestionWithImages),\n/* harmony export */   deleteQuestion: () => (/* binding */ deleteQuestion),\n/* harmony export */   getQuestionById: () => (/* binding */ getQuestionById),\n/* harmony export */   getQuestions: () => (/* binding */ getQuestions),\n/* harmony export */   getQuestionsByDifficulty: () => (/* binding */ getQuestionsByDifficulty),\n/* harmony export */   getQuestionsBySubjectAndTopic: () => (/* binding */ getQuestionsBySubjectAndTopic),\n/* harmony export */   updateQuestion: () => (/* binding */ updateQuestion),\n/* harmony export */   updateQuestionWithImages: () => (/* binding */ updateQuestionWithImages)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:3000/api\" || 0;\n/**\n * Create a new question\n * @param questionData The question data\n * @returns The created question\n */ async function createQuestion(questionData) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        // Create a copy of the data to avoid modifying the original\n        const dataToSend = {\n            ...questionData\n        };\n        // Remove fields that should not be sent to the API\n        if (!dataToSend.explanation || dataToSend.explanation.trim() === '') {\n            delete dataToSend.explanation;\n        }\n        // Remove status and reviewStatus as they're rejected by the API\n        delete dataToSend.status;\n        delete dataToSend.reviewStatus;\n        // Set default type if not provided\n        if (!dataToSend.type) {\n            dataToSend.type = 'multiple-choice';\n        }\n        console.log(\"Sending question data:\", JSON.stringify(dataToSend, null, 2));\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/questions\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: JSON.stringify(dataToSend)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"API error response:\", errorData);\n            // Check if we have detailed validation errors\n            if (errorData.details) {\n                const errorMessages = Object.entries(errorData.details).map((param)=>{\n                    let [field, message] = param;\n                    return \"\".concat(field, \": \").concat(message);\n                }).join(', ');\n                throw new Error(errorMessages || errorData.message || \"Error: \".concat(response.status));\n            }\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error creating question:\", error);\n        throw error;\n    }\n}\n/**\n * Get all questions\n * @returns List of questions\n */ async function getQuestions() {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/questions\"), {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching questions:\", error);\n        throw error;\n    }\n}\n/**\n * Get a question by ID\n * @param id Question ID\n * @returns The question\n */ async function getQuestionById(id) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(id), {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question:\", error);\n        throw error;\n    }\n}\n/**\n * Update a question\n * @param id Question ID\n * @param questionData The updated question data\n * @returns The updated question\n */ async function updateQuestion(id, questionData) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(id), {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: JSON.stringify(questionData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error updating question:\", error);\n        throw error;\n    }\n}\n/**\n * Update a question with optional new images (PATCH method)\n * @param id Question ID\n * @param questionData The updated question data\n * @param questionImage Optional new question image file\n * @param optionImages Optional new option images\n * @returns The updated question\n */ async function updateQuestionWithImages(id, questionData, questionImage, optionImages) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const formData = new FormData();\n        // Add question data\n        formData.append('content', questionData.content);\n        // Add options as individual form fields\n        questionData.options.forEach((option, index)=>{\n            formData.append(\"options[\".concat(index, \"]\"), option);\n        });\n        formData.append('answer', questionData.answer);\n        formData.append('subjectId', questionData.subjectId);\n        formData.append('topicId', questionData.topicId);\n        formData.append('difficulty', questionData.difficulty);\n        formData.append('type', questionData.type || 'multiple-choice');\n        // Add createdBy field if provided\n        if (questionData.createdBy) {\n            formData.append('createdBy', questionData.createdBy);\n        }\n        // Only add explanation if it has a value\n        if (questionData.explanation && questionData.explanation.trim() !== '') {\n            formData.append('explanation', questionData.explanation);\n        }\n        // Add question image if provided\n        if (questionImage) {\n            formData.append('images', questionImage);\n        }\n        // Add option images if provided\n        if (optionImages) {\n            Object.entries(optionImages).forEach((param)=>{\n                let [key, file] = param;\n                if (file) {\n                    formData.append(\"optionImages[\".concat(key, \"]\"), file);\n                }\n            });\n        }\n        const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(id), {\n            method: \"PATCH\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: formData\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error updating question with images:\", error);\n        throw error;\n    }\n}\n/**\n * Delete a question\n * @param id Question ID\n * @returns The deleted question\n */ async function deleteQuestion(id) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(id), {\n            method: \"DELETE\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error deleting question:\", error);\n        throw error;\n    }\n}\n/**\n * Create a question with images\n * @param questionData The question data without imageUrls\n * @param questionImage Optional question image file\n * @param optionImages Optional map of option images\n * @returns The created question\n */ async function createQuestionWithImages(questionData, questionImage, optionImages) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const formData = new FormData();\n        // Add question data\n        formData.append('content', questionData.content);\n        // Add options as individual form fields\n        questionData.options.forEach((option, index)=>{\n            formData.append(\"options[\".concat(index, \"]\"), option);\n        });\n        formData.append('answer', questionData.answer);\n        formData.append('subjectId', questionData.subjectId);\n        formData.append('topicId', questionData.topicId);\n        formData.append('difficulty', questionData.difficulty);\n        // Add type field with default if not provided\n        formData.append('type', questionData.type || 'multiple-choice');\n        // Add createdBy field if provided\n        if (questionData.createdBy) {\n            formData.append('createdBy', questionData.createdBy);\n        }\n        // Only add explanation if it has a value\n        if (questionData.explanation && questionData.explanation.trim() !== '') {\n            formData.append('explanation', questionData.explanation);\n        }\n        // Add question image if provided\n        if (questionImage) {\n            formData.append('images', questionImage);\n        }\n        // Add option images if provided\n        if (optionImages) {\n            Object.entries(optionImages).forEach((param)=>{\n                let [key, file] = param;\n                if (file) {\n                    formData.append(\"optionImages[\".concat(key, \"]\"), file);\n                }\n            });\n        }\n        // Log form data entries for debugging\n        console.log(\"Form data entries:\");\n        for (const pair of formData.entries()){\n            console.log(pair[0], pair[1]);\n        }\n        const response = await fetch(\"\".concat(baseUrl, \"/questions\"), {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            },\n            body: formData\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            console.error(\"API error response:\", errorData);\n            // Check if we have detailed validation errors\n            if (errorData.details) {\n                const errorMessages = Object.entries(errorData.details).map((param)=>{\n                    let [field, message] = param;\n                    return \"\".concat(field, \": \").concat(message);\n                }).join(', ');\n                throw new Error(errorMessages || errorData.message || \"Error: \".concat(response.status));\n            }\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error creating question with images:\", error);\n        throw error;\n    }\n}\n/**\n * Get questions by subject and topic\n * @param subjectId Subject ID\n * @param topicId Topic ID\n * @returns List of questions\n */ async function getQuestionsBySubjectAndTopic(subjectId, topicId) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const url = \"\".concat(baseUrl, \"/questions?subjectId=\").concat(encodeURIComponent(subjectId), \"&topicId=\").concat(encodeURIComponent(topicId));\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching questions by subject and topic:\", error);\n        throw error;\n    }\n}\n/**\n * Get questions by difficulty\n * @param difficulty Difficulty level ('easy', 'medium', 'hard')\n * @returns List of questions\n */ async function getQuestionsByDifficulty(difficulty) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const url = \"\".concat(baseUrl, \"/questions?difficulty=\").concat(encodeURIComponent(difficulty));\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching \".concat(difficulty, \" questions:\"), error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/questions.ts\n"));

/***/ })

});