import { ConfigService } from '@nestjs/config';
import { Model } from 'mongoose';
import { Question } from '../schema/question.schema';
import { ImageCompressionService } from '../common/services/image-compression.service';
interface MistralOCRResponse {
    id: string;
    object: string;
    model: string;
    usage: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
    content: string;
    images?: Array<{
        bbox: number[];
        base64: string;
    }>;
}
interface ParsedQuestion {
    content: string;
    options: string[];
    answer: string;
    imageUrls?: string[];
    difficulty: 'easy' | 'medium' | 'hard';
    type: string;
}
interface BulkUploadResult {
    questionsAdded: number;
    questionsFailed: number;
    questions: Question[];
    errors: string[];
}
export declare class MistralAiService {
    private readonly configService;
    private questionModel;
    private readonly imageCompressionService;
    private readonly logger;
    constructor(configService: ConfigService, questionModel: Model<Question>, imageCompressionService: ImageCompressionService);
    processPdfWithOCR(file: Express.Multer.File): Promise<MistralOCRResponse>;
    parseQuestionsFromOCR(ocrResult: MistralOCRResponse): Promise<ParsedQuestion[]>;
    private extractQuestionsFromText;
    private parseQuestionBlock;
    private extractOptions;
    private compressAndSaveImages;
    private associateImagesWithQuestions;
    bulkCreateQuestions(parsedQuestions: ParsedQuestion[], subjectId: string, userId: string): Promise<BulkUploadResult>;
}
export {};
