"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var MistralAiService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MistralAiService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const question_schema_1 = require("../schema/question.schema");
const image_compression_service_1 = require("../common/services/image-compression.service");
let MistralAiService = MistralAiService_1 = class MistralAiService {
    constructor(configService, questionModel, imageCompressionService) {
        this.configService = configService;
        this.questionModel = questionModel;
        this.imageCompressionService = imageCompressionService;
        this.logger = new common_1.Logger(MistralAiService_1.name);
    }
    async processPdfWithOCR(file) {
        try {
            const mistralApiKey = this.configService.get('MISTRAL_API_KEY');
            if (!mistralApiKey) {
                throw new common_1.BadRequestException('Mistral API key not configured');
            }
            const base64Pdf = file.buffer.toString('base64');
            const response = await fetch('https://api.mistral.ai/v1/ocr', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${mistralApiKey}`,
                },
                body: JSON.stringify({
                    model: 'mistral-ocr-latest',
                    document: {
                        type: 'document_url',
                        document_url: `data:application/pdf;base64,${base64Pdf}`,
                    },
                    include_image_base64: true,
                }),
            });
            if (!response.ok) {
                const errorText = await response.text();
                this.logger.error(`Mistral OCR API error: ${response.status} - ${errorText}`);
                throw new common_1.BadRequestException(`OCR processing failed: ${response.statusText}`);
            }
            const ocrResult = await response.json();
            this.logger.log(`OCR processing completed. Content length: ${ocrResult.content?.length || 0}`);
            return ocrResult;
        }
        catch (error) {
            this.logger.error(`Error processing PDF with OCR: ${error.message}`, error.stack);
            throw error;
        }
    }
    async parseQuestionsFromOCR(ocrResult) {
        try {
            const content = ocrResult.content;
            const images = ocrResult.images || [];
            const questions = this.extractQuestionsFromText(content);
            const imageUrls = await this.compressAndSaveImages(images);
            const questionsWithImages = this.associateImagesWithQuestions(questions, imageUrls);
            return questionsWithImages;
        }
        catch (error) {
            this.logger.error(`Error parsing questions from OCR: ${error.message}`, error.stack);
            throw error;
        }
    }
    extractQuestionsFromText(content) {
        const questions = [];
        const questionBlocks = content.split(/(?:^|\n)(?:Q\.?\s*\d+\.?|Question\s+\d+\.?|\d+\.)\s*/i);
        for (let i = 1; i < questionBlocks.length; i++) {
            const block = questionBlocks[i].trim();
            if (!block)
                continue;
            try {
                const parsedQuestion = this.parseQuestionBlock(block);
                if (parsedQuestion) {
                    questions.push(parsedQuestion);
                }
            }
            catch (error) {
                this.logger.warn(`Failed to parse question block: ${error.message}`);
            }
        }
        return questions;
    }
    parseQuestionBlock(block) {
        const optionPattern = /(?:^|\n)\s*(?:[A-D]\.?|\([A-D]\))\s+/i;
        const optionMatch = block.search(optionPattern);
        if (optionMatch === -1) {
            return null;
        }
        const questionContent = block.substring(0, optionMatch).trim();
        const optionsText = block.substring(optionMatch);
        const options = this.extractOptions(optionsText);
        if (options.length < 2) {
            return null;
        }
        const answerPattern = /(?:Answer|Ans)\.?\s*:?\s*([A-D])/i;
        const answerMatch = block.match(answerPattern);
        const answerLetter = answerMatch ? answerMatch[1].toUpperCase() : 'A';
        const answerIndex = answerLetter.charCodeAt(0) - 'A'.charCodeAt(0);
        const answer = options[answerIndex] || options[0];
        return {
            content: questionContent,
            options,
            answer,
            difficulty: 'medium',
            type: 'multiple-choice',
        };
    }
    extractOptions(optionsText) {
        const options = [];
        const optionLines = optionsText.split(/\n/);
        for (const line of optionLines) {
            const match = line.match(/^\s*(?:[A-D]\.?|\([A-D]\))\s+(.+)/i);
            if (match) {
                options.push(match[1].trim());
            }
        }
        return options;
    }
    async compressAndSaveImages(images) {
        const imageUrls = [];
        for (const [index, image] of images.entries()) {
            try {
                const result = await this.imageCompressionService.compressBase64Image(image.base64, {
                    maxSizeBytes: 2 * 1024 * 1024,
                    quality: 85,
                    format: 'jpeg',
                });
                imageUrls.push(result.url);
                this.logger.log(`Compressed and saved image: ${result.filename} (${result.compressionRatio}% reduction)`);
            }
            catch (error) {
                this.logger.error(`Failed to compress and save image ${index}: ${error.message}`);
            }
        }
        return imageUrls;
    }
    associateImagesWithQuestions(questions, imageUrls) {
        const questionsWithImages = questions.map((question, index) => {
            const assignedImages = imageUrls.slice(Math.floor((index * imageUrls.length) / questions.length), Math.floor(((index + 1) * imageUrls.length) / questions.length));
            return {
                ...question,
                imageUrls: assignedImages.length > 0 ? assignedImages : undefined,
            };
        });
        return questionsWithImages;
    }
    async bulkCreateQuestions(parsedQuestions, subjectId, userId) {
        const result = {
            questionsAdded: 0,
            questionsFailed: 0,
            questions: [],
            errors: [],
        };
        for (const parsedQuestion of parsedQuestions) {
            try {
                const questionData = {
                    content: parsedQuestion.content,
                    options: parsedQuestion.options,
                    answer: parsedQuestion.answer,
                    imageUrls: parsedQuestion.imageUrls || [],
                    subjectId,
                    difficulty: parsedQuestion.difficulty,
                    type: parsedQuestion.type,
                    createdBy: userId,
                    reviewStatus: 'pending',
                    status: 'inactive',
                    source: 'pdf-ocr',
                };
                const createdQuestion = new this.questionModel(questionData);
                const savedQuestion = await createdQuestion.save();
                result.questions.push(savedQuestion);
                result.questionsAdded++;
                this.logger.log(`Created question: ${savedQuestion._id}`);
            }
            catch (error) {
                result.questionsFailed++;
                result.errors.push(`Failed to create question: ${error.message}`);
                this.logger.error(`Failed to create question: ${error.message}`);
            }
        }
        return result;
    }
};
exports.MistralAiService = MistralAiService;
exports.MistralAiService = MistralAiService = MistralAiService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, mongoose_1.InjectModel)(question_schema_1.Question.name)),
    __metadata("design:paramtypes", [config_1.ConfigService,
        mongoose_2.Model,
        image_compression_service_1.ImageCompressionService])
], MistralAiService);
//# sourceMappingURL=mistral-ai.service.js.map