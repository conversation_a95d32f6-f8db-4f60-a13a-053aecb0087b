"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/edit-question/[id]/page",{

/***/ "(app-pages-browser)/./src/components/admin/edit-question-form.tsx":
/*!*****************************************************!*\
  !*** ./src/components/admin/edit-question-form.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditQuestionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/api/subjects */ \"(app-pages-browser)/./src/lib/api/subjects.ts\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/imageUtils */ \"(app-pages-browser)/./src/utils/imageUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB\n;\nconst ACCEPTED_FILE_TYPES = [\n    \"image/jpeg\",\n    \"image/jpg\",\n    \"image/png\",\n    \"image/svg+xml\"\n];\n// Form schema with custom validation for options\nconst formSchema = zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n    subject: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, {\n        message: \"Please select a subject\"\n    }),\n    topic: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, {\n        message: \"Please select a topic\"\n    }),\n    questionText: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(5, {\n        message: \"Question must be at least 5 characters\"\n    }),\n    optionA: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n    optionB: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n    optionC: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n    optionD: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n    correctAnswer: zod__WEBPACK_IMPORTED_MODULE_16__.z.enum([\n        \"A\",\n        \"B\",\n        \"C\",\n        \"D\"\n    ], {\n        required_error: \"Please select the correct answer\"\n    }),\n    explanation: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n    difficulty: zod__WEBPACK_IMPORTED_MODULE_16__.z.enum([\n        \"Easy\",\n        \"Medium\",\n        \"Hard\"\n    ], {\n        required_error: \"Please select a difficulty level\"\n    })\n});\nfunction EditQuestionForm(param) {\n    let { questionData, questionId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [questionImage, setQuestionImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [optionImages, setOptionImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: null,\n        B: null,\n        C: null,\n        D: null\n    });\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topics, setTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [optionValidationErrors, setOptionValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: false,\n        B: false,\n        C: false,\n        D: false\n    });\n    // Refs for file inputs\n    const questionImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const optionImageRefs = {\n        A: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        B: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        C: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        D: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null)\n    };\n    // Initialize form with question data\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(formSchema),\n        defaultValues: {\n            subject: \"\",\n            topic: \"\",\n            questionText: \"\",\n            optionA: \"\",\n            optionB: \"\",\n            optionC: \"\",\n            optionD: \"\",\n            correctAnswer: \"\",\n            explanation: \"\",\n            difficulty: \"\"\n        }\n    });\n    // Parse existing question data and populate form\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditQuestionForm.useEffect\": ()=>{\n            if (questionData && subjects.length > 0) {\n                var _questionData_options;\n                // Parse options from the question data\n                const parseOptions = {\n                    \"EditQuestionForm.useEffect.parseOptions\": ()=>{\n                        if (!questionData.options || questionData.options.length === 0) return [\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\"\n                        ];\n                        if (typeof questionData.options[0] === 'string') {\n                            if (questionData.options.length === 1 && questionData.options[0].includes(',')) {\n                                // Single comma-separated string\n                                const optionTexts = questionData.options[0].split(',');\n                                return optionTexts.map({\n                                    \"EditQuestionForm.useEffect.parseOptions\": (text)=>text.trim()\n                                }[\"EditQuestionForm.useEffect.parseOptions\"]).concat(Array(4 - optionTexts.length).fill(\"\")).slice(0, 4);\n                            } else {\n                                // Array of individual strings\n                                return questionData.options.concat(Array(4 - questionData.options.length).fill(\"\")).slice(0, 4);\n                            }\n                        }\n                        return [\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\"\n                        ];\n                    }\n                }[\"EditQuestionForm.useEffect.parseOptions\"];\n                const parsedOptions = parseOptions();\n                const newOptionImages = {\n                    A: null,\n                    B: null,\n                    C: null,\n                    D: null\n                };\n                // Check for base64 images in options and extract them\n                parsedOptions.forEach({\n                    \"EditQuestionForm.useEffect\": (option, index)=>{\n                        const optionKey = String.fromCharCode(65 + index); // A, B, C, D\n                        if (typeof option === 'string' && (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_15__.isBase64Image)(option)) {\n                            newOptionImages[optionKey] = (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_15__.ensureDataUrl)(option);\n                            parsedOptions[index] = \"\"; // Clear text since it's an image\n                        }\n                    }\n                }[\"EditQuestionForm.useEffect\"]);\n                setOptionImages(newOptionImages);\n                var _questionData_options_findIndex;\n                // Find correct answer letter\n                const answerIndex = (_questionData_options_findIndex = (_questionData_options = questionData.options) === null || _questionData_options === void 0 ? void 0 : _questionData_options.findIndex({\n                    \"EditQuestionForm.useEffect\": (opt)=>opt === questionData.answer\n                }[\"EditQuestionForm.useEffect\"])) !== null && _questionData_options_findIndex !== void 0 ? _questionData_options_findIndex : -1;\n                const correctAnswerLetter = answerIndex >= 0 ? String.fromCharCode(65 + answerIndex) : \"A\";\n                // Handle topicId - it could be a string ID or a populated object\n                const topicId = questionData.topicId ? typeof questionData.topicId === 'string' ? questionData.topicId : questionData.topicId._id : \"\";\n                // Set form values\n                form.reset({\n                    subject: questionData.subjectId._id,\n                    topic: topicId,\n                    questionText: questionData.content,\n                    optionA: parsedOptions[0] || \"\",\n                    optionB: parsedOptions[1] || \"\",\n                    optionC: parsedOptions[2] || \"\",\n                    optionD: parsedOptions[3] || \"\",\n                    correctAnswer: correctAnswerLetter,\n                    explanation: questionData.explanation || \"\",\n                    difficulty: questionData.difficulty.charAt(0).toUpperCase() + questionData.difficulty.slice(1)\n                });\n                // Set topics for the selected subject\n                const selectedSubject = subjects.find({\n                    \"EditQuestionForm.useEffect.selectedSubject\": (s)=>s._id === questionData.subjectId._id\n                }[\"EditQuestionForm.useEffect.selectedSubject\"]);\n                if (selectedSubject) {\n                    setTopics(selectedSubject.topics || []);\n                }\n            }\n        }\n    }[\"EditQuestionForm.useEffect\"], [\n        questionData,\n        subjects,\n        form\n    ]);\n    // Fetch subjects and topics from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditQuestionForm.useEffect\": ()=>{\n            const fetchSubjectsAndTopics = {\n                \"EditQuestionForm.useEffect.fetchSubjectsAndTopics\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const data = await (0,_lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__.getSubjectsWithTopics)();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects and topics:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                            title: \"Error\",\n                            description: error.message || \"Failed to load subjects and topics\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"EditQuestionForm.useEffect.fetchSubjectsAndTopics\"];\n            fetchSubjectsAndTopics();\n        }\n    }[\"EditQuestionForm.useEffect\"], []);\n    // Handle subject change to update topics\n    const handleSubjectChange = (value)=>{\n        form.setValue(\"subject\", value);\n        form.setValue(\"topic\", \"\");\n        // Find the selected subject and set its topics\n        const selectedSubject = subjects.find((subject)=>subject._id === value);\n        if (selectedSubject) {\n            setTopics(selectedSubject.topics || []);\n        } else {\n            setTopics([]);\n        }\n    };\n    // Handle image upload\n    const handleImageUpload = (e, type)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            if (type === \"question\") {\n                var _event_target;\n                setQuestionImage((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result);\n            } else {\n                setOptionImages((prev)=>{\n                    var _event_target;\n                    return {\n                        ...prev,\n                        [type]: (_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result\n                    };\n                });\n                // Clear validation error for this option\n                setOptionValidationErrors((prev)=>({\n                        ...prev,\n                        [type]: false\n                    }));\n            }\n        };\n        reader.readAsDataURL(file);\n    };\n    // Remove image\n    const removeImage = (type)=>{\n        if (type === \"question\") {\n            setQuestionImage(null);\n            if (questionImageRef.current) {\n                questionImageRef.current.value = \"\";\n            }\n        } else {\n            var _optionImageRefs_type;\n            setOptionImages((prev)=>({\n                    ...prev,\n                    [type]: null\n                }));\n            if ((_optionImageRefs_type = optionImageRefs[type]) === null || _optionImageRefs_type === void 0 ? void 0 : _optionImageRefs_type.current) {\n                optionImageRefs[type].current.value = \"\";\n            }\n            // Clear form validation error for this option if it exists\n            form.clearErrors(\"option\".concat(type));\n            // Update validation state\n            setOptionValidationErrors((prev)=>({\n                    ...prev,\n                    [type]: false\n                }));\n        }\n    };\n    // Custom validation function\n    const validateOptions = (formData)=>{\n        const errors = [];\n        const validationState = {};\n        const options = [\n            'A',\n            'B',\n            'C',\n            'D'\n        ];\n        for (const option of options){\n            const hasText = formData[\"option\".concat(option)] && formData[\"option\".concat(option)].trim() !== '';\n            const hasImage = optionImages[option] !== null;\n            if (!hasText && !hasImage) {\n                errors.push(\"Option \".concat(option, \" must have either text or an image\"));\n                validationState[option] = true;\n            } else {\n                validationState[option] = false;\n            }\n        }\n        // Update validation state\n        setOptionValidationErrors(validationState);\n        return errors;\n    };\n    // Handle form submission\n    const onSubmit = async (data)=>{\n        setIsSubmitting(true);\n        try {\n            var _data_optionA, _data_optionB, _data_optionC, _data_optionD;\n            console.log(\"Form data:\", data);\n            // Validate that each option has either text or image\n            const validationErrors = validateOptions(data);\n            if (validationErrors.length > 0) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                    title: \"Validation Error\",\n                    description: validationErrors.join(', '),\n                    variant: \"destructive\"\n                });\n                setIsSubmitting(false);\n                return;\n            }\n            // Convert option data to array format expected by API\n            // Use image base64 if no text, otherwise use text\n            const options = [\n                ((_data_optionA = data.optionA) === null || _data_optionA === void 0 ? void 0 : _data_optionA.trim()) || optionImages.A || '',\n                ((_data_optionB = data.optionB) === null || _data_optionB === void 0 ? void 0 : _data_optionB.trim()) || optionImages.B || '',\n                ((_data_optionC = data.optionC) === null || _data_optionC === void 0 ? void 0 : _data_optionC.trim()) || optionImages.C || '',\n                ((_data_optionD = data.optionD) === null || _data_optionD === void 0 ? void 0 : _data_optionD.trim()) || optionImages.D || ''\n            ];\n            // Map correctAnswer (A, B, C, D) to the actual option value\n            const answerMap = {\n                A: 0,\n                B: 1,\n                C: 2,\n                D: 3\n            };\n            const answerIndex = answerMap[data.correctAnswer];\n            const answer = options[answerIndex];\n            // Convert difficulty to lowercase to match API expectations\n            const difficulty = data.difficulty.toLowerCase();\n            // Create base question data\n            const baseQuestionData = {\n                content: data.questionText,\n                options,\n                answer,\n                subjectId: data.subject,\n                topicId: data.topic,\n                difficulty,\n                type: \"multiple-choice\"\n            };\n            // Only add explanation if it has a value\n            const questionData = data.explanation && data.explanation.trim() !== '' ? {\n                ...baseQuestionData,\n                explanation: data.explanation\n            } : baseQuestionData;\n            console.log(\"Prepared question data:\", questionData);\n            // The backend expects images embedded as base64 in options, not as separate files\n            console.log(\"Updating question with embedded base64 images\");\n            let finalQuestionData = {\n                ...questionData\n            };\n            // If question has an image, embed it in the question text as base64\n            if (questionImage) {\n                finalQuestionData.content = \"\".concat(questionData.content, \"\\n\").concat(questionImage);\n            }\n            // Submit to API using JSON (the options already contain base64 images where needed)\n            await updateQuestion(questionId, finalQuestionData);\n            // Display success toast\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                title: \"Question Updated\",\n                description: \"Your question has been successfully updated.\"\n            });\n            // Redirect back to question bank\n            router.push(\"/admin/question-bank\");\n        } catch (error) {\n            console.error(\"Error updating question:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                title: \"Error\",\n                description: error.message || \"Failed to update question. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Cancel and go back\n    const handleCancel = ()=>{\n        router.push(\"/admin/question-bank\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                lineNumber: 386,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n            lineNumber: 385,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n            className: \"w-full max-w-4xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.Form, {\n                    ...form,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: form.handleSubmit(onSubmit),\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                        control: form.control,\n                                        name: \"subject\",\n                                        render: (param)=>{\n                                            let { field } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                        children: \"Subject *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                        onValueChange: handleSubjectChange,\n                                                        value: field.value,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                        placeholder: \"Select a subject\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 408,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                children: subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                        value: subject._id,\n                                                                        children: subject.name\n                                                                    }, subject._id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 29\n                                                                    }, void 0))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 21\n                                            }, void 0);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                        control: form.control,\n                                        name: \"topic\",\n                                        render: (param)=>{\n                                            let { field } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                        children: \"Topic *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                        onValueChange: field.onChange,\n                                                        value: field.value,\n                                                        disabled: topics.length === 0,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                        placeholder: topics.length === 0 ? \"Select a subject first\" : \"Select a topic\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 433,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 431,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                children: topics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                        value: topic._id,\n                                                                        children: topic.name\n                                                                    }, topic._id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 438,\n                                                                        columnNumber: 29\n                                                                    }, void 0))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 21\n                                            }, void 0);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"questionText\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: \"Question Text *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__.Textarea, {\n                                                    placeholder: \"Enter your question here...\",\n                                                    className: \"min-h-[100px]\",\n                                                    ...field\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                        children: \"Question Image (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                onClick: ()=>{\n                                                    var _questionImageRef_current;\n                                                    return (_questionImageRef_current = questionImageRef.current) === null || _questionImageRef_current === void 0 ? void 0 : _questionImageRef_current.click();\n                                                },\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Upload Image\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ref: questionImageRef,\n                                                type: \"file\",\n                                                accept: ACCEPTED_FILE_TYPES.join(\",\"),\n                                                onChange: (e)=>handleImageUpload(e, \"question\"),\n                                                className: \"hidden\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 19\n                                            }, this),\n                                            questionImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: questionImage,\n                                                        alt: \"Question\",\n                                                        width: 100,\n                                                        height: 100,\n                                                        className: \"rounded-md object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"destructive\",\n                                                        size: \"icon\",\n                                                        className: \"absolute -top-2 -right-2 h-6 w-6\",\n                                                        onClick: ()=>removeImage(\"question\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                        children: \"Answer Options *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            \"A\",\n                                            \"B\",\n                                            \"C\",\n                                            \"D\"\n                                        ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                        control: form.control,\n                                                        name: \"option\".concat(option),\n                                                        render: (param)=>{\n                                                            let { field } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                        children: [\n                                                                            \"Option \",\n                                                                            option,\n                                                                            optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-green-600 ml-2\",\n                                                                                children: \"(Image uploaded)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                                lineNumber: 526,\n                                                                                columnNumber: 33\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 523,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                            placeholder: optionImages[option] ? \"Option \".concat(option, \" text (optional - image uploaded)\") : \"Enter option \".concat(option, \" text or upload an image...\"),\n                                                                            ...field\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 31\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 529,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 539,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    optionValidationErrors[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-red-600\",\n                                                                        children: [\n                                                                            \"Option \",\n                                                                            option,\n                                                                            \" requires either text or an image\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 541,\n                                                                        columnNumber: 31\n                                                                    }, void 0)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 27\n                                                            }, void 0);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>{\n                                                                    var _optionImageRefs_option_current, _optionImageRefs_option;\n                                                                    return (_optionImageRefs_option = optionImageRefs[option]) === null || _optionImageRefs_option === void 0 ? void 0 : (_optionImageRefs_option_current = _optionImageRefs_option.current) === null || _optionImageRefs_option_current === void 0 ? void 0 : _optionImageRefs_option_current.click();\n                                                                },\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Image\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                ref: optionImageRefs[option],\n                                                                type: \"file\",\n                                                                accept: ACCEPTED_FILE_TYPES.join(\",\"),\n                                                                onChange: (e)=>handleImageUpload(e, option),\n                                                                className: \"hidden\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                        src: optionImages[option],\n                                                                        alt: \"Option \".concat(option),\n                                                                        width: 60,\n                                                                        height: 60,\n                                                                        className: \"rounded-md object-cover\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 570,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        type: \"button\",\n                                                                        variant: \"destructive\",\n                                                                        size: \"icon\",\n                                                                        className: \"absolute -top-1 -right-1 h-4 w-4\",\n                                                                        onClick: ()=>removeImage(option),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"h-2 w-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                            lineNumber: 584,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 577,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, option, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"correctAnswer\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: \"Correct Answer *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_9__.RadioGroup, {\n                                                    onValueChange: field.onChange,\n                                                    value: field.value,\n                                                    className: \"flex flex-row space-x-6\",\n                                                    children: [\n                                                        \"A\",\n                                                        \"B\",\n                                                        \"C\",\n                                                        \"D\"\n                                                    ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_9__.RadioGroupItem, {\n                                                                    value: option,\n                                                                    id: option\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 609,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                    htmlFor: option,\n                                                                    children: [\n                                                                        \"Option \",\n                                                                        option\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 610,\n                                                                    columnNumber: 29\n                                                                }, void 0)\n                                                            ]\n                                                        }, option, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 27\n                                                        }, void 0))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"difficulty\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: \"Difficulty Level *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                onValueChange: field.onChange,\n                                                value: field.value,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                placeholder: \"Select difficulty level\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 630,\n                                                                columnNumber: 27\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 25\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"Easy\",\n                                                                children: \"Easy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"Medium\",\n                                                                children: \"Medium\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"Hard\",\n                                                                children: \"Hard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"explanation\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: [\n                                                    \"Explanation (Optional)\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.TooltipTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"inline h-4 w-4 ml-1 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 654,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.TooltipContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Provide an explanation for the correct answer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 656,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__.Textarea, {\n                                                    placeholder: \"Explain why this is the correct answer...\",\n                                                    className: \"min-h-[80px]\",\n                                                    ...field\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 645,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-4 pt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        onClick: handleCancel,\n                                        disabled: isSubmitting,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Updating...\"\n                                            ]\n                                        }, void 0, true) : \"Update Question\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 674,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                lineNumber: 394,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n            lineNumber: 393,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n        lineNumber: 392,\n        columnNumber: 5\n    }, this);\n}\n_s(EditQuestionForm, \"F/d5wKHUivNzAWSLkvfvZ9N8nyk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm\n    ];\n});\n_c = EditQuestionForm;\nvar _c;\n$RefreshReg$(_c, \"EditQuestionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/edit-question-form.tsx\n"));

/***/ })

});