"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/question-bank/question-bank.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionBank)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _question_list__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./question-list */ \"(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx\");\n/* harmony import */ var _components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/pagination */ \"(app-pages-browser)/./src/components/ui/pagination.tsx\");\n/* harmony import */ var _question_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./question-skeleton */ \"(app-pages-browser)/./src/components/admin/question-bank/question-skeleton.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction QuestionBank() {\n    _s();\n    // State for subjects and topics\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topics, setTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // State for filters\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_subjects\");\n    const [selectedTopic, setSelectedTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_topics\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // State for questions and pagination\n    const [questions, setQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch subjects with topics\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchSubjectsWithTopics = {\n                \"QuestionBank.useEffect.fetchSubjectsWithTopics\": async ()=>{\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        const response = await fetch(\"\".concat(baseUrl, \"/subjects/with-topics\"), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch subjects: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load subjects. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchSubjectsWithTopics\"];\n            fetchSubjectsWithTopics();\n        }\n    }[\"QuestionBank.useEffect\"], []);\n    // Update topics when subject changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            if (selectedSubject && selectedSubject !== \"all_subjects\") {\n                const selectedSubjectObj = subjects.find({\n                    \"QuestionBank.useEffect.selectedSubjectObj\": (s)=>s._id === selectedSubject\n                }[\"QuestionBank.useEffect.selectedSubjectObj\"]);\n                if (selectedSubjectObj && selectedSubjectObj.topics) {\n                    setTopics(selectedSubjectObj.topics);\n                } else {\n                    setTopics([]);\n                }\n                setSelectedTopic(\"all_topics\");\n            } else {\n                setTopics([]);\n            }\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        subjects\n    ]);\n    // Fetch questions with filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchQuestions = {\n                \"QuestionBank.useEffect.fetchQuestions\": async ()=>{\n                    setLoading(true);\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        // Build query parameters\n                        const params = new URLSearchParams();\n                        if (selectedSubject && selectedSubject !== \"all_subjects\") params.append('subjectId', selectedSubject);\n                        if (selectedTopic && selectedTopic !== \"all_topics\") params.append('topicId', selectedTopic);\n                        if (searchQuery) params.append('search', searchQuery);\n                        params.append('page', pagination.currentPage.toString());\n                        params.append('limit', pagination.itemsPerPage.toString());\n                        const response = await fetch(\"\".concat(baseUrl, \"/questions?\").concat(params.toString()), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch questions: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setQuestions(data.questions);\n                        setPagination(data.pagination);\n                    } catch (error) {\n                        console.error(\"Error fetching questions:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load questions. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchQuestions\"];\n            fetchQuestions();\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        selectedTopic,\n        searchQuery,\n        pagination.currentPage\n    ]);\n    // Handle page change\n    const handlePageChange = (pageNumber)=>{\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: pageNumber\n            }));\n    };\n    // Handle difficulty change\n    const handleDifficultyChange = async (questionId, difficulty)=>{\n        try {\n            const baseUrl = \"http://localhost:3000/api\" || 0;\n            const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(questionId), {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                },\n                body: JSON.stringify({\n                    difficulty\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update question: \".concat(response.status));\n            }\n            // Update local state\n            setQuestions((prev)=>prev.map((q)=>q._id === questionId ? {\n                        ...q,\n                        difficulty\n                    } : q));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Question difficulty updated successfully\"\n            });\n        } catch (error) {\n            console.error(\"Error updating question difficulty:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update question difficulty\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Format questions for the QuestionList component\n    const formattedQuestions = questions.map((q)=>{\n        var _topics_find;\n        let parsedOptions = [];\n        if (q.options && q.options.length > 0) {\n            if (typeof q.options[0] === 'string') {\n                // Check if it's a single comma-separated string or an array of individual strings\n                if (q.options.length === 1 && q.options[0].includes(',')) {\n                    // Single comma-separated string: [\"Paris,London,Berlin,Madrid\"]\n                    const optionTexts = q.options[0].split(',');\n                    parsedOptions = optionTexts.map((text, index)=>({\n                            label: String.fromCharCode(97 + index),\n                            text: text.trim()\n                        }));\n                } else {\n                    // Array of individual strings: [\"Cerebrum\", \"Cerebellum\", \"Medulla\", \"Pons\"]\n                    parsedOptions = q.options.map((text, index)=>({\n                            label: String.fromCharCode(97 + index),\n                            text: text.trim()\n                        }));\n                }\n            } else {\n                // If options is already an array of objects\n                parsedOptions = q.options.map((opt, index)=>({\n                        label: String.fromCharCode(97 + index),\n                        text: typeof opt === 'string' ? opt : opt.text || '',\n                        imageUrl: typeof opt === 'object' ? opt.imageUrl : undefined\n                    }));\n            }\n        }\n        return {\n            id: q._id,\n            subject: q.subjectId.name,\n            topic: ((_topics_find = topics.find((t)=>t._id === q.topicId)) === null || _topics_find === void 0 ? void 0 : _topics_find.name) || \"Unknown\",\n            text: q.content,\n            options: parsedOptions,\n            difficulty: q.difficulty.charAt(0).toUpperCase() + q.difficulty.slice(1),\n            correctAnswer: q.answer,\n            reviewStatus: q.reviewStatus\n        };\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Subject\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedSubject,\n                                onValueChange: setSelectedSubject,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: \"Select Subject\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_subjects\",\n                                                children: \"All Subjects\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 15\n                                            }, this),\n                                            subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: subject._id,\n                                                    children: subject.name\n                                                }, subject._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Topic\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedTopic,\n                                onValueChange: setSelectedTopic,\n                                disabled: selectedSubject === \"all_subjects\" || topics.length === 0,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: selectedSubject !== \"all_subjects\" ? \"Select Topic\" : \"Select Subject First\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_topics\",\n                                                children: \"All Topics\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 15\n                                            }, this),\n                                            topics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: topic._id,\n                                                    children: topic.name\n                                                }, topic._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        type: \"text\",\n                                        placeholder: \"Search questions...\",\n                                        className: \"pl-8\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 273,\n                columnNumber: 9\n            }, this) : formattedQuestions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_list__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        questions: formattedQuestions,\n                        onDifficultyChange: handleDifficultyChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, this),\n                    pagination.totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__.Pagination, {\n                        currentPage: pagination.currentPage,\n                        totalPages: pagination.totalPages,\n                        onPageChange: handlePageChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"No questions found. Try adjusting your filters.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 292,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionBank, \"XGbmSMRGFnf0M4WXBBZhb4wrgnk=\");\n_c = QuestionBank;\nvar _c;\n$RefreshReg$(_c, \"QuestionBank\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/pagination.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/pagination.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Pagination: () => (/* binding */ Pagination)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n\n\n\n\nfunction Pagination(param) {\n    let { currentPage, totalPages, onPageChange, pageSize, totalItems, onPageSizeChange, pageSizeOptions = [\n        5,\n        10,\n        20,\n        50\n    ] } = param;\n    const startItem = Math.min(totalItems, (currentPage - 1) * pageSize + 1);\n    const endItem = Math.min(totalItems, currentPage * pageSize);\n    // Generate page numbers to display\n    const getPageNumbers = ()=>{\n        const pages = [];\n        const maxPagesToShow = 5;\n        if (totalPages <= maxPagesToShow) {\n            // Show all pages if total pages is less than maxPagesToShow\n            for(let i = 1; i <= totalPages; i++){\n                pages.push(i);\n            }\n        } else {\n            // Add first page\n            pages.push(1);\n            // Add ellipsis if needed\n            if (currentPage > 3) {\n                pages.push('ellipsis');\n            }\n            // Add pages around current page\n            const startPage = Math.max(2, currentPage - 1);\n            const endPage = Math.min(totalPages - 1, currentPage + 1);\n            for(let i = startPage; i <= endPage; i++){\n                pages.push(i);\n            }\n            // Add ellipsis if needed\n            if (currentPage < totalPages - 2) {\n                pages.push('ellipsis');\n            }\n            // Add last page if not already added\n            if (totalPages > 1) {\n                pages.push(totalPages);\n            }\n        }\n        return pages;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-between px-2 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 text-sm text-muted-foreground\",\n                children: totalItems > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        \"Showing \",\n                        startItem,\n                        \" to \",\n                        endItem,\n                        \" of \",\n                        totalItems,\n                        \" items\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"No items\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-6\",\n                children: [\n                    onPageSizeChange && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Rows per page\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                className: \"h-8 w-[70px] rounded-md border border-input bg-background px-3 py-1 text-sm\",\n                                value: pageSize,\n                                onChange: (e)=>onPageSizeChange(Number(e.target.value)),\n                                children: pageSizeOptions.map((size)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: size,\n                                        children: size\n                                    }, size, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>onPageChange(currentPage - 1),\n                                disabled: currentPage === 1,\n                                className: \"h-8 w-8 p-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Previous page\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            getPageNumbers().map((page, index)=>page === 'ellipsis' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2\",\n                                    children: \"...\"\n                                }, \"ellipsis-\".concat(index), false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: currentPage === page ? \"default\" : \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>onPageChange(page),\n                                    className: \"h-8 w-8 p-0\",\n                                    children: page\n                                }, \"page-\".concat(page), false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>onPageChange(currentPage + 1),\n                                disabled: currentPage === totalPages || totalPages === 0,\n                                className: \"h-8 w-8 p-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Next page\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n_c = Pagination;\nvar _c;\n$RefreshReg$(_c, \"Pagination\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/pagination.tsx\n"));

/***/ })

});