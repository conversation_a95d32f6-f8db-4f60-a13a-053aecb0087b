import { Model, Document, Types } from 'mongoose';
import { QuestionPaper } from '../schema/question-paper.schema';
import { Question } from '../schema/question.schema';
import { Subject } from '../schema/subject.schema';
import { TrackingService } from '../common/services/tracking.service';
import { QuestionUsageService } from '../question-usage/question-usage.service';
import { CreateQuestionPaperDto } from './dto/create-question-paper.dto';
import { SetQuestionLimitDto } from './dto/set-question-limit.dto';
import { UpdateQuestionPaperDto } from './dto/update-question-paper.dto';
export declare class QuestionPapersService {
    private questionPaperModel;
    private questionModel;
    private subjectModel;
    private trackingService;
    private questionUsageService;
    private readonly logger;
    constructor(questionPaperModel: Model<QuestionPaper>, questionModel: Model<Question>, subjectModel: Model<Subject>, trackingService: TrackingService, questionUsageService: QuestionUsageService);
    createUnified(createQuestionPaperDto: CreateQuestionPaperDto, user: any): Promise<QuestionPaper>;
    findAll(user: any): Promise<(Document<unknown, {}, QuestionPaper> & QuestionPaper & {
        _id: Types.ObjectId;
    } & {
        __v: number;
    })[]>;
    findOne(id: string, user: any): Promise<Document<unknown, {}, QuestionPaper> & QuestionPaper & {
        _id: Types.ObjectId;
    } & {
        __v: number;
    }>;
    update(id: string, updateQuestionPaperDto: UpdateQuestionPaperDto, user: any): Promise<(Document<unknown, {}, QuestionPaper> & QuestionPaper & {
        _id: Types.ObjectId;
    } & {
        __v: number;
    }) | null>;
    checkDownloadLimits(user: any, questionPaper: any): Promise<void>;
    download(id: string, format?: 'pdf' | 'docx', user?: any): Promise<string>;
    setQuestionLimit(setQuestionLimitDto: SetQuestionLimitDto): Promise<void>;
    private generatePDF;
    private generateDOCX;
    private resolveSubject;
    private findSubjectByShortCode;
    private recordQuestionUsage;
    private checkGenerationLimits;
    private filterUnusedQuestions;
    private selectQuestionsByDifficulty;
}
