"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/add-question/page",{

/***/ "(app-pages-browser)/./src/components/admin/add-question-form.tsx":
/*!****************************************************!*\
  !*** ./src/components/admin/add-question-form.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddQuestionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _file_uploader__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./file-uploader */ \"(app-pages-browser)/./src/components/admin/file-uploader.tsx\");\n/* harmony import */ var _lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/api/subjects */ \"(app-pages-browser)/./src/lib/api/subjects.ts\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB\n;\nconst ACCEPTED_FILE_TYPES = [\n    \"image/jpeg\",\n    \"image/jpg\",\n    \"image/png\",\n    \"image/svg+xml\"\n];\n// Form schema with custom validation for options\nconst formSchema = zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n    subject: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, {\n        message: \"Please select a subject\"\n    }),\n    topic: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, {\n        message: \"Please select a topic\"\n    }),\n    questionText: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(5, {\n        message: \"Question must be at least 5 characters\"\n    }),\n    optionA: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n    optionB: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n    optionC: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n    optionD: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n    correctAnswer: zod__WEBPACK_IMPORTED_MODULE_16__.z.enum([\n        \"A\",\n        \"B\",\n        \"C\",\n        \"D\"\n    ], {\n        required_error: \"Please select the correct answer\"\n    }),\n    explanation: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n    difficulty: zod__WEBPACK_IMPORTED_MODULE_16__.z.enum([\n        \"Easy\",\n        \"Medium\",\n        \"Hard\"\n    ], {\n        required_error: \"Please select a difficulty level\"\n    }),\n    question_options: zod__WEBPACK_IMPORTED_MODULE_16__.z.any().optional()\n});\nfunction AddQuestionForm() {\n    _s();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [questionImage, setQuestionImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [optionImages, setOptionImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: null,\n        B: null,\n        C: null,\n        D: null\n    });\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topics, setTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [optionValidationErrors, setOptionValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: false,\n        B: false,\n        C: false,\n        D: false\n    });\n    // Fetch subjects and topics from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddQuestionForm.useEffect\": ()=>{\n            const fetchSubjectsAndTopics = {\n                \"AddQuestionForm.useEffect.fetchSubjectsAndTopics\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const data = await (0,_lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__.getSubjectsWithTopics)();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects and topics:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                            title: \"Error\",\n                            description: error.message || \"Failed to load subjects and topics\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AddQuestionForm.useEffect.fetchSubjectsAndTopics\"];\n            fetchSubjectsAndTopics();\n        }\n    }[\"AddQuestionForm.useEffect\"], []);\n    // Refs for file inputs\n    const questionImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const optionImageRefs = {\n        A: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        B: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        C: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        D: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null)\n    };\n    // Initialize form with empty string values instead of undefined\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(formSchema),\n        defaultValues: {\n            subject: \"\",\n            topic: \"\",\n            questionText: \"\",\n            optionA: \"\",\n            optionB: \"\",\n            optionC: \"\",\n            optionD: \"\",\n            correctAnswer: \"\",\n            explanation: \"\",\n            difficulty: \"\",\n            question_options: null\n        }\n    });\n    // Handle subject change to update topics\n    const handleSubjectChange = (value)=>{\n        form.setValue(\"subject\", value);\n        form.setValue(\"topic\", \"\");\n        // Find the selected subject and set its topics\n        const selectedSubject = subjects.find((subject)=>subject._id === value);\n        if (selectedSubject) {\n            setTopics(selectedSubject.topics || []);\n        } else {\n            setTopics([]);\n        }\n    };\n    // Handle image upload\n    const handleImageUpload = (e, type)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            if (type === \"question\") {\n                var _event_target;\n                setQuestionImage((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result);\n            } else {\n                setOptionImages((prev)=>{\n                    var _event_target;\n                    return {\n                        ...prev,\n                        [type]: (_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result\n                    };\n                });\n                // Clear validation error for this option\n                setOptionValidationErrors((prev)=>({\n                        ...prev,\n                        [type]: false\n                    }));\n            }\n        };\n        reader.readAsDataURL(file);\n    };\n    // Remove image\n    const removeImage = (type)=>{\n        if (type === \"question\") {\n            setQuestionImage(null);\n            if (questionImageRef.current) {\n                questionImageRef.current.value = \"\";\n            }\n        } else {\n            var _optionImageRefs_type;\n            setOptionImages((prev)=>({\n                    ...prev,\n                    [type]: null\n                }));\n            if ((_optionImageRefs_type = optionImageRefs[type]) === null || _optionImageRefs_type === void 0 ? void 0 : _optionImageRefs_type.current) {\n                optionImageRefs[type].current.value = \"\";\n            }\n            // Clear form validation error for this option if it exists\n            form.clearErrors(\"option\".concat(type));\n            // Update validation state\n            setOptionValidationErrors((prev)=>({\n                    ...prev,\n                    [type]: false\n                }));\n        }\n    };\n    // Custom validation function\n    const validateOptions = (formData)=>{\n        const errors = [];\n        const validationState = {};\n        const options = [\n            'A',\n            'B',\n            'C',\n            'D'\n        ];\n        for (const option of options){\n            const hasText = formData[\"option\".concat(option)] && formData[\"option\".concat(option)].trim() !== '';\n            const hasImage = optionImages[option] !== null;\n            if (!hasText && !hasImage) {\n                errors.push(\"Option \".concat(option, \" must have either text or an image\"));\n                validationState[option] = true;\n            } else {\n                validationState[option] = false;\n            }\n        }\n        // Update validation state\n        setOptionValidationErrors(validationState);\n        return errors;\n    };\n    // Handle form submission\n    const onSubmit = async (data)=>{\n        setIsSubmitting(true);\n        try {\n            var _data_optionA, _data_optionB, _data_optionC, _data_optionD;\n            console.log(\"Form data:\", data);\n            // Validate that each option has either text or image\n            const validationErrors = validateOptions(data);\n            if (validationErrors.length > 0) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                    title: \"Validation Error\",\n                    description: validationErrors.join(', '),\n                    variant: \"destructive\"\n                });\n                setIsSubmitting(false);\n                return;\n            }\n            // Convert option data to array format expected by API\n            // Use image base64 if no text, otherwise use text\n            const options = [\n                ((_data_optionA = data.optionA) === null || _data_optionA === void 0 ? void 0 : _data_optionA.trim()) || optionImages.A || '',\n                ((_data_optionB = data.optionB) === null || _data_optionB === void 0 ? void 0 : _data_optionB.trim()) || optionImages.B || '',\n                ((_data_optionC = data.optionC) === null || _data_optionC === void 0 ? void 0 : _data_optionC.trim()) || optionImages.C || '',\n                ((_data_optionD = data.optionD) === null || _data_optionD === void 0 ? void 0 : _data_optionD.trim()) || optionImages.D || ''\n            ];\n            // Map correctAnswer (A, B, C, D) to the actual option value\n            const answerMap = {\n                A: 0,\n                B: 1,\n                C: 2,\n                D: 3\n            };\n            const answerIndex = answerMap[data.correctAnswer];\n            const answer = options[answerIndex];\n            // Convert difficulty to lowercase to match API expectations\n            const difficulty = data.difficulty.toLowerCase();\n            // Get user ID from localStorage if available\n            const userData = localStorage.getItem(\"userData\");\n            let userId;\n            try {\n                if (userData) {\n                    const parsed = JSON.parse(userData);\n                    userId = parsed._id || parsed.id;\n                    console.log(\"User data from localStorage:\", parsed);\n                }\n            } catch (e) {\n                console.error(\"Error parsing user data:\", e);\n            }\n            // Create base question data\n            const baseQuestionData = {\n                content: data.questionText,\n                options,\n                answer,\n                subjectId: data.subject,\n                topicId: data.topic,\n                difficulty,\n                type: \"multiple-choice\"\n            };\n            // Only add createdBy if we have a valid user ID\n            if (userId) {\n                baseQuestionData.createdBy = userId;\n            }\n            // Only add explanation if it has a value\n            const questionData = data.explanation && data.explanation.trim() !== '' ? {\n                ...baseQuestionData,\n                explanation: data.explanation\n            } : baseQuestionData;\n            console.log(\"Prepared question data:\", questionData);\n            // For now, we'll convert all images to base64 and include them in the options\n            // This matches the format expected by the question bank display\n            console.log(\"Submitting question with base64 images embedded in options\");\n            // If question has an image, we could embed it in the question text\n            // For now, we'll focus on option images being converted to base64\n            let finalQuestionData = {\n                ...questionData\n            };\n            // If question has an image, embed it in the question text as base64\n            if (questionImage) {\n                finalQuestionData.content = \"\".concat(questionData.content, \"\\n\").concat(questionImage);\n            }\n            // Submit to API - the options already contain base64 images where needed\n            await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_15__.createQuestion)(finalQuestionData);\n            // Display success toast\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"Question Added\",\n                description: \"Your question has been successfully added to the question bank.\"\n            });\n            // Reset form with empty strings for radio values\n            form.reset({\n                subject: \"\",\n                topic: \"\",\n                questionText: \"\",\n                optionA: \"\",\n                optionB: \"\",\n                optionC: \"\",\n                optionD: \"\",\n                correctAnswer: \"\",\n                explanation: \"\",\n                difficulty: \"\",\n                question_options: null\n            });\n            // Reset other state\n            setQuestionImage(null);\n            setOptionImages({\n                A: null,\n                B: null,\n                C: null,\n                D: null\n            });\n            setTopics([]);\n            setOptionValidationErrors({\n                A: false,\n                B: false,\n                C: false,\n                D: false\n            });\n        } catch (error) {\n            console.error(\"Error adding question:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_12__.toast)({\n                title: \"Error\",\n                description: error.message || \"Failed to add question. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Reset form\n    const resetForm = ()=>{\n        form.reset({\n            subject: \"\",\n            topic: \"\",\n            questionText: \"\",\n            optionA: \"\",\n            optionB: \"\",\n            optionC: \"\",\n            optionD: \"\",\n            correctAnswer: \"\",\n            explanation: \"\",\n            difficulty: \"\",\n            question_options: null\n        });\n        setQuestionImage(null);\n        setOptionImages({\n            A: null,\n            B: null,\n            C: null,\n            D: null\n        });\n        setTopics([]);\n        setOptionValidationErrors({\n            A: false,\n            B: false,\n            C: false,\n            D: false\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n        className: \"w-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n            className: \"pt-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.Form, {\n                ...form,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: form.handleSubmit(onSubmit),\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                    control: form.control,\n                                    name: \"subject\",\n                                    render: (param)=>{\n                                        let { field } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                    children: \"Subject\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                    onValueChange: handleSubjectChange,\n                                                    value: field.value,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                    placeholder: loading ? \"Loading subjects...\" : \"Select a subject\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                value: \"loading\",\n                                                                disabled: true,\n                                                                children: \"Loading subjects...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 27\n                                                            }, void 0) : subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                    value: subject._id,\n                                                                    children: subject.name\n                                                                }, subject._id, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 418,\n                                                                    columnNumber: 29\n                                                                }, void 0))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 19\n                                        }, void 0);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                    control: form.control,\n                                    name: \"topic\",\n                                    render: (param)=>{\n                                        let { field } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                    children: \"Topic\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                    onValueChange: field.onChange,\n                                                    value: field.value,\n                                                    disabled: topics.length === 0,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                    placeholder: loading ? \"Loading topics...\" : topics.length > 0 ? \"Select a topic\" : \"Select a subject first\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                            children: topics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                    value: topic._id,\n                                                                    children: topic.name\n                                                                }, topic._id, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 27\n                                                                }, void 0))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 19\n                                        }, void 0);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                    control: form.control,\n                                    name: \"questionText\",\n                                    render: (param)=>{\n                                        let { field } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                    children: \"Question Text\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                        placeholder: \"Enter your question here...\",\n                                                        className: \"min-h-[100px]\",\n                                                        ...field\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 19\n                                        }, void 0);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Question Image (Optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipProvider, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Upload an image to accompany your question\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"h-9\",\n                                                    onClick: ()=>{\n                                                        var _questionImageRef_current;\n                                                        return (_questionImageRef_current = questionImageRef.current) === null || _questionImageRef_current === void 0 ? void 0 : _questionImageRef_current.click();\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Upload Image\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"file\",\n                                                    ref: questionImageRef,\n                                                    className: \"hidden\",\n                                                    accept: \"image/*\",\n                                                    onChange: (e)=>handleImageUpload(e, \"question\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 19\n                                                }, this),\n                                                questionImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: questionImage || \"/placeholder.svg\",\n                                                            alt: \"Question image\",\n                                                            width: 100,\n                                                            height: 100,\n                                                            className: \"object-cover rounded-md border h-[100px] w-[100px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"destructive\",\n                                                            size: \"icon\",\n                                                            className: \"h-6 w-6 absolute -top-2 -right-2 rounded-full\",\n                                                            onClick: ()=>removeImage(\"question\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 531,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium\",\n                                    children: \"Answer Options\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 15\n                                }, this),\n                                [\n                                    \"A\",\n                                    \"B\",\n                                    \"C\",\n                                    \"D\"\n                                ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-[1fr,auto] gap-4 items-start border-b pb-4 last:border-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                                control: form.control,\n                                                name: \"option\".concat(option),\n                                                render: (param)=>{\n                                                    let { field } = param;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                children: [\n                                                                    \"Option \",\n                                                                    option,\n                                                                    optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-green-600 ml-2\",\n                                                                        children: \"(Image uploaded)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 556,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 553,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    placeholder: optionImages[option] ? \"Option \".concat(option, \" text (optional - image uploaded)\") : \"Enter option \".concat(option, \" text or upload an image...\"),\n                                                                    ...field\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 560,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            optionValidationErrors[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-red-600\",\n                                                                children: [\n                                                                    \"Option \",\n                                                                    option,\n                                                                    \" requires either text or an image\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 27\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 23\n                                                    }, void 0);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 mt-8 md:mt-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"h-9 text-xs\",\n                                                            onClick: ()=>{\n                                                                var _optionImageRefs_option_current;\n                                                                return (_optionImageRefs_option_current = optionImageRefs[option].current) === null || _optionImageRefs_option_current === void 0 ? void 0 : _optionImageRefs_option_current.click();\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-3 w-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 589,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Image\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"file\",\n                                                            ref: optionImageRefs[option],\n                                                            className: \"hidden\",\n                                                            accept: \"image/*\",\n                                                            onChange: (e)=>handleImageUpload(e, option)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    src: optionImages[option] || \"/placeholder.svg\",\n                                                                    alt: \"Option \".concat(option, \" image\"),\n                                                                    width: 60,\n                                                                    height: 60,\n                                                                    className: \"object-cover rounded-md border h-[60px] w-[60px]\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 602,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"destructive\",\n                                                                    size: \"icon\",\n                                                                    className: \"h-5 w-5 absolute -top-2 -right-2 rounded-full\",\n                                                                    onClick: ()=>removeImage(option),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 616,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 609,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, option, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 17\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                    control: form.control,\n                                    name: \"correctAnswer\",\n                                    render: (param)=>{\n                                        let { field } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                    children: \"Correct Answer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroup, {\n                                                        onValueChange: field.onChange,\n                                                        value: field.value || \"\",\n                                                        className: \"flex space-x-4\",\n                                                        children: [\n                                                            \"A\",\n                                                            \"B\",\n                                                            \"C\",\n                                                            \"D\"\n                                                        ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroupItem, {\n                                                                            value: option,\n                                                                            id: \"option-\".concat(option)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 643,\n                                                                            columnNumber: 31\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 642,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                        className: \"font-normal\",\n                                                                        htmlFor: \"option-\".concat(option),\n                                                                        children: option\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 645,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                ]\n                                                            }, option, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 27\n                                                            }, void 0))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 19\n                                        }, void 0);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                                    control: form.control,\n                                    name: \"difficulty\",\n                                    render: (param)=>{\n                                        let { field } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                    children: \"Difficulty Level\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroup, {\n                                                        onValueChange: field.onChange,\n                                                        value: field.value || \"\",\n                                                        className: \"flex space-x-4\",\n                                                        children: [\n                                                            \"Easy\",\n                                                            \"Medium\",\n                                                            \"Hard\"\n                                                        ].map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_8__.RadioGroupItem, {\n                                                                            value: level,\n                                                                            id: \"level-\".concat(level)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                            lineNumber: 672,\n                                                                            columnNumber: 31\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 671,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                                        className: \"font-normal\",\n                                                                        htmlFor: \"level-\".concat(level),\n                                                                        children: level\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                        lineNumber: 674,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                ]\n                                                            }, level, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 670,\n                                                                columnNumber: 27\n                                                            }, void 0))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 19\n                                        }, void 0);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 657,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 627,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                            control: form.control,\n                            name: \"explanation\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                                    children: \"Explanation (Optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipProvider, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 698,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Provide an explanation for the correct answer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                    lineNumber: 701,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                        lineNumber: 696,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                placeholder: \"Explain why the correct answer is right...\",\n                                                className: \"min-h-[80px]\",\n                                                ...field\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                lineNumber: 707,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormDescription, {\n                                            children: \"This will be shown to students after they answer the question.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 713,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 692,\n                                    columnNumber: 17\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 688,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormField, {\n                            control: form.control,\n                            name: \"question_options\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormLabel, {\n                                            children: \"Upload Questions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 15\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_file_uploader__WEBPACK_IMPORTED_MODULE_13__.FileUploader, {\n                                                value: field.value,\n                                                onChange: (files)=>{\n                                                    field.onChange(files);\n                                                },\n                                                maxSize: MAX_FILE_SIZE,\n                                                acceptedTypes: ACCEPTED_FILE_TYPES\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 17\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 727,\n                                            columnNumber: 15\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_6__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                            lineNumber: 737,\n                                            columnNumber: 15\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 725,\n                                    columnNumber: 13\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-3 pt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"submit\",\n                                    className: \"w-full bg-blue-500 hover:bg-blue-600 sm:w-auto\",\n                                    disabled: isSubmitting,\n                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Submitting...\"\n                                        ]\n                                    }, void 0, true) : \"Add Question\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 742,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    className: \"w-full sm:w-auto\",\n                                    onClick: resetForm,\n                                    disabled: isSubmitting,\n                                    children: \"Reset Form\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                                    lineNumber: 752,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                            lineNumber: 741,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n                lineNumber: 395,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n            lineNumber: 394,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\add-question-form.tsx\",\n        lineNumber: 393,\n        columnNumber: 5\n    }, this);\n}\n_s(AddQuestionForm, \"NvQNK4hvd7zivpFJI4sVpGGgbH0=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm\n    ];\n});\n_c = AddQuestionForm;\nvar _c;\n$RefreshReg$(_c, \"AddQuestionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/add-question-form.tsx\n"));

/***/ })

});