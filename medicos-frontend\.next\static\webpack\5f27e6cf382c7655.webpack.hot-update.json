{"c": ["app/layout", "app/admin/question-bank/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cadars%5C%5CDesktop%5C%5CFL%5C%5Cmedicos%5C%5Cmedicos-frontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cquestion-bank%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/admin/question-bank/page.tsx", "(app-pages-browser)/./src/components/admin/question-bank/pagination.tsx", "(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx", "(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx", "(app-pages-browser)/./src/components/admin/question-bank/question-skeleton.tsx"]}