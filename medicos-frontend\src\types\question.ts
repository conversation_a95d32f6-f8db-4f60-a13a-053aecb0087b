// Shared types for questions
export interface QuestionOption {
  label: string;
  text: string;
  imageUrl?: string;
  isImageOption?: boolean; // Flag to indicate if this option is primarily an image
}

export interface FormattedQuestion {
  id: string;
  subject: string;
  topic: string;
  text: string;
  options: QuestionOption[];
  difficulty: string;
  correctAnswer: string;
  reviewStatus: string;
}

export interface ApiQuestion {
  _id: string;
  content: string;
  options: string[] | any[];
  answer: string;
  subjectId: {
    _id: string;
    name: string;
  };
  topicId?: string;
  difficulty: string;
  type: string;
  status: string;
  reviewStatus: string;
  createdAt: string;
}

export interface Subject {
  _id: string;
  name: string;
  topics?: Topic[];
}

export interface Topic {
  _id: string;
  name: string;
  subjectId?: string;
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}