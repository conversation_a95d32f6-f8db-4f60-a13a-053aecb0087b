"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/question-bank/question-bank.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionBank)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _question_list__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./question-list */ \"(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx\");\n/* harmony import */ var _pagination__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pagination */ \"(app-pages-browser)/./src/components/admin/question-bank/pagination.tsx\");\n/* harmony import */ var _question_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./question-skeleton */ \"(app-pages-browser)/./src/components/admin/question-bank/question-skeleton.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction QuestionBank() {\n    _s();\n    // State for subjects and topics\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topics, setTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // State for filters\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_subjects\");\n    const [selectedTopic, setSelectedTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_topics\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // State for questions and pagination\n    const [questions, setQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch subjects with topics\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchSubjectsWithTopics = {\n                \"QuestionBank.useEffect.fetchSubjectsWithTopics\": async ()=>{\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        const response = await fetch(\"\".concat(baseUrl, \"/subjects/with-topics\"), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch subjects: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load subjects. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchSubjectsWithTopics\"];\n            fetchSubjectsWithTopics();\n        }\n    }[\"QuestionBank.useEffect\"], []);\n    // Update topics when subject changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            if (selectedSubject && selectedSubject !== \"all_subjects\") {\n                const selectedSubjectObj = subjects.find({\n                    \"QuestionBank.useEffect.selectedSubjectObj\": (s)=>s._id === selectedSubject\n                }[\"QuestionBank.useEffect.selectedSubjectObj\"]);\n                if (selectedSubjectObj && selectedSubjectObj.topics) {\n                    setTopics(selectedSubjectObj.topics);\n                } else {\n                    setTopics([]);\n                }\n                setSelectedTopic(\"all_topics\");\n            } else {\n                setTopics([]);\n            }\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        subjects\n    ]);\n    // Fetch questions with filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchQuestions = {\n                \"QuestionBank.useEffect.fetchQuestions\": async ()=>{\n                    setLoading(true);\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        // Build query parameters\n                        const params = new URLSearchParams();\n                        if (selectedSubject) params.append('subjectId', selectedSubject);\n                        if (selectedTopic) params.append('topicId', selectedTopic);\n                        if (searchQuery) params.append('search', searchQuery);\n                        params.append('page', pagination.currentPage.toString());\n                        params.append('limit', pagination.itemsPerPage.toString());\n                        const response = await fetch(\"\".concat(baseUrl, \"/questions?\").concat(params.toString()), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch questions: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setQuestions(data.questions);\n                        setPagination(data.pagination);\n                    } catch (error) {\n                        console.error(\"Error fetching questions:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load questions. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchQuestions\"];\n            fetchQuestions();\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        selectedTopic,\n        searchQuery,\n        pagination.currentPage\n    ]);\n    // Handle page change\n    const handlePageChange = (pageNumber)=>{\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: pageNumber\n            }));\n    };\n    // Handle difficulty change\n    const handleDifficultyChange = async (questionId, difficulty)=>{\n        try {\n            const baseUrl = \"http://localhost:3000/api\" || 0;\n            const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(questionId), {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                },\n                body: JSON.stringify({\n                    difficulty\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update question: \".concat(response.status));\n            }\n            // Update local state\n            setQuestions((prev)=>prev.map((q)=>q._id === questionId ? {\n                        ...q,\n                        difficulty\n                    } : q));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Question difficulty updated successfully\"\n            });\n        } catch (error) {\n            console.error(\"Error updating question difficulty:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update question difficulty\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Format questions for the QuestionList component\n    const formattedQuestions = questions.map((q)=>{\n        var _topics_find;\n        let parsedOptions = [];\n        if (q.options && q.options.length > 0) {\n            if (typeof q.options[0] === 'string') {\n                // If options is a string array, split by commas and create option objects\n                const optionTexts = q.options[0].split(',');\n                parsedOptions = optionTexts.map((text, index)=>({\n                        label: String.fromCharCode(97 + index),\n                        text: text.trim()\n                    }));\n            } else {\n                // If options is already an array of objects\n                parsedOptions = q.options.map((opt, index)=>({\n                        label: String.fromCharCode(97 + index),\n                        text: typeof opt === 'string' ? opt : opt.text || '',\n                        imageUrl: typeof opt === 'object' ? opt.imageUrl : undefined\n                    }));\n            }\n        }\n        return {\n            id: q._id,\n            subject: q.subjectId.name,\n            topic: ((_topics_find = topics.find((t)=>t._id === q.topicId)) === null || _topics_find === void 0 ? void 0 : _topics_find.name) || \"Unknown\",\n            text: q.content,\n            options: parsedOptions,\n            difficulty: q.difficulty.charAt(0).toUpperCase() + q.difficulty.slice(1),\n            correctAnswer: q.answer,\n            reviewStatus: q.reviewStatus\n        };\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Subject\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedSubject,\n                                onValueChange: setSelectedSubject,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: \"Select Subject\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"\",\n                                                children: \"All Subjects\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 15\n                                            }, this),\n                                            subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: subject._id,\n                                                    children: subject.name\n                                                }, subject._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Topic\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedTopic,\n                                onValueChange: setSelectedTopic,\n                                disabled: !selectedSubject || topics.length === 0,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: selectedSubject ? \"Select Topic\" : \"Select Subject First\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"\",\n                                                children: \"All Topics\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 15\n                                            }, this),\n                                            topics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: topic._id,\n                                                    children: topic.name\n                                                }, topic._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        type: \"text\",\n                                        placeholder: \"Search questions...\",\n                                        className: \"pl-8\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 264,\n                columnNumber: 9\n            }, this) : formattedQuestions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_list__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        questions: formattedQuestions,\n                        onDifficultyChange: handleDifficultyChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 11\n                    }, this),\n                    pagination.totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pagination__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        currentPage: pagination.currentPage,\n                        totalPages: pagination.totalPages,\n                        onPageChange: handlePageChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"No questions found. Try adjusting your filters.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 283,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n        lineNumber: 206,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionBank, \"XGbmSMRGFnf0M4WXBBZhb4wrgnk=\");\n_c = QuestionBank;\nvar _c;\n$RefreshReg$(_c, \"QuestionBank\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx\n"));

/***/ })

});