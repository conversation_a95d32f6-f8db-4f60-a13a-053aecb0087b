"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var QuestionPapersController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestionPapersController = void 0;
const common_1 = require("@nestjs/common");
const path = require("path");
const question_papers_service_1 = require("./question-papers.service");
const create_question_paper_dto_1 = require("./dto/create-question-paper.dto");
const update_question_paper_dto_1 = require("./dto/update-question-paper.dto");
const set_question_limit_dto_1 = require("./dto/set-question-limit.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
const role_enum_1 = require("../auth/enums/role.enum");
const swagger_1 = require("@nestjs/swagger");
const services_1 = require("../common/services");
let QuestionPapersController = QuestionPapersController_1 = class QuestionPapersController {
    constructor(questionPapersService, trackingService) {
        this.questionPapersService = questionPapersService;
        this.trackingService = trackingService;
        this.logger = new common_1.Logger(QuestionPapersController_1.name);
    }
    async create(req, createQuestionPaperDto) {
        const questionPaper = await this.questionPapersService.createUnified(createQuestionPaperDto, req.user);
        await this.trackingService.trackPaperGeneration({
            userId: req.user._id,
            paperId: questionPaper._id.toString(),
            collegeId: req.user.collegeId,
            subjectId: questionPaper.subjectId.toString(),
            ipAddress: req.ip,
        });
        return questionPaper;
    }
    findAll(req) {
        return this.questionPapersService.findAll(req.user);
    }
    findOne(req, id) {
        return this.questionPapersService.findOne(id, req.user);
    }
    async download(id, format = 'pdf', req, res) {
        try {
            if (format !== 'pdf' && format !== 'docx') {
                return res.status(400).json({
                    statusCode: 400,
                    message: `Invalid format: ${format}. Supported formats are 'pdf' and 'docx'.`,
                });
            }
            const filePath = await this.questionPapersService.download(id, format, req.user);
            await this.trackingService.trackDownload({
                userId: req.user._id,
                paperId: id,
                collegeId: req.user.collegeId,
                downloadFormat: format,
                ipAddress: req.ip,
            });
            return res.download(filePath, path.basename(filePath), (err) => {
                if (err) {
                    this.logger.error(`Error sending file: ${err.message}`, err.stack);
                    if (!res.headersSent) {
                        return res.status(500).json({
                            statusCode: 500,
                            message: 'Error sending file',
                        });
                    }
                }
            });
        }
        catch (error) {
            this.logger.error(`Error in download: ${error.message}`, error.stack);
            if (!res.headersSent) {
                const status = error.status || 500;
                const message = error.message || 'Internal server error';
                return res.status(status).json({
                    statusCode: status,
                    message,
                });
            }
        }
    }
    setQuestionLimit(setQuestionLimitDto) {
        return this.questionPapersService.setQuestionLimit(setQuestionLimitDto);
    }
    update(req, id, updateQuestionPaperDto) {
        return this.questionPapersService.update(id, updateQuestionPaperDto, req.user);
    }
};
exports.QuestionPapersController = QuestionPapersController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.TEACHER, role_enum_1.Role.SUPER_ADMIN),
    (0, swagger_1.ApiOperation)({
        summary: 'Create question paper (Unified)',
        description: 'Create a new question paper with automatic or customized generation. If customise object is provided, generates customized question paper. Otherwise, generates automatic question paper. Questions are global (any college can use any approved question), but reuse prevention is applied per college.',
    }),
    (0, swagger_1.ApiBody)({
        type: create_question_paper_dto_1.CreateQuestionPaperDto,
        description: 'Question paper configuration with automatic or customized generation options',
        examples: {
            'Automatic Question Paper': {
                summary: 'Automatic question paper generation',
                value: {
                    title: 'NEET Physics Mock Test 2024',
                    subject: 'physics',
                    examType: 'NEET',
                    totalMarks: 100,
                    duration: 180,
                    instructions: 'Read all questions carefully before answering',
                },
            },
            'Customized Question Paper': {
                summary: 'Customized question paper with specific configuration',
                value: {
                    title: 'Custom Physics Test 2024',
                    subject: 'physics',
                    examType: 'CUSTOM',
                    totalMarks: 100,
                    duration: 180,
                    instructions: 'Read all questions carefully',
                    customise: {
                        customDifficulty: {
                            easyPercentage: 30,
                            mediumPercentage: 50,
                            hardPercentage: 20,
                        },
                        numberOfQuestions: 50,
                        totalMarks: 100,
                        duration: 180,
                        includeAnswers: true,
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Question paper created successfully',
        schema: {
            type: 'object',
            properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                title: { type: 'string', example: 'NEET Physics Mock Test 2024' },
                description: {
                    type: 'string',
                    example: 'Final examination for Physics course',
                },
                subjectId: { type: 'string', example: '60d21b4667d0d8992e610c86' },
                topicId: { type: 'string', example: '60d21b4667d0d8992e610c87' },
                totalMarks: { type: 'number', example: 100 },
                duration: { type: 'number', example: 180 },
                withAnswers: { type: 'boolean', example: true },
                instructions: {
                    type: 'string',
                    example: 'Read all questions carefully before answering',
                },
                examType: { type: 'string', example: 'NEET' },
                difficultyMode: { type: 'string', example: 'custom' },
                questions: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            _id: { type: 'string', example: '60d21b4667d0d8992e610c88' },
                            content: { type: 'string', example: 'What is the derivative of f(x) = x²?' },
                            options: { type: 'array', items: { type: 'string' }, example: ['2x', 'x', '2', '1'] },
                            answer: { type: 'string', example: '2x' },
                            difficulty: { type: 'string', example: 'medium' },
                            type: { type: 'string', example: 'multiple-choice' },
                            marks: { type: 'number', example: 4 },
                        },
                    },
                },
                generatedBy: { type: 'string', example: '60d21b4667d0d8992e610c90' },
                collegeId: { type: 'string', example: '60d21b4667d0d8992e610c91' },
                status: { type: 'string', example: 'active' },
                sections: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            name: { type: 'string', example: 'Section A' },
                            description: { type: 'string', example: 'All Questions' },
                            order: { type: 'number', example: 1 },
                            sectionMarks: { type: 'number', example: 100 },
                            questions: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        question: {
                                            type: 'object',
                                            properties: {
                                                _id: { type: 'string', example: '60d21b4667d0d8992e610c88' },
                                                content: { type: 'string', example: 'What is the derivative of f(x) = x²?' },
                                                options: { type: 'array', items: { type: 'string' }, example: ['2x', 'x', '2', '1'] },
                                                answer: { type: 'string', example: '2x' },
                                                difficulty: { type: 'string', example: 'medium' },
                                                type: { type: 'string', example: 'multiple-choice' },
                                                marks: { type: 'number', example: 4 },
                                            },
                                        },
                                        order: { type: 'number', example: 1 },
                                    },
                                },
                            },
                        },
                    },
                },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Bad request - Invalid input or limit exceeded',
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_question_paper_dto_1.CreateQuestionPaperDto]),
    __metadata("design:returntype", Promise)
], QuestionPapersController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.TEACHER, role_enum_1.Role.COLLEGE_ADMIN, role_enum_1.Role.SUPER_ADMIN),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all question papers based on user role',
        description: 'Returns question papers based on user role: Teachers see only their own papers, College Admins see all papers from their college with teacher details, Super Admins see all papers with college-wise grouping.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns all question papers',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                    title: { type: 'string', example: 'Mathematics Final Exam 2024' },
                    subjectId: {
                        type: 'object',
                        properties: {
                            _id: { type: 'string', example: '60d21b4667d0d8992e610c86' },
                            name: { type: 'string', example: 'Mathematics' },
                        },
                    },
                    totalMarks: { type: 'number', example: 100 },
                    duration: { type: 'number', example: 180 },
                    generatedBy: { type: 'string', example: '60d21b4667d0d8992e610c90' },
                    collegeId: { type: 'string', example: '60d21b4667d0d8992e610c91' },
                    status: { type: 'string', example: 'active' },
                    createdAt: { type: 'string', format: 'date-time' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], QuestionPapersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.TEACHER, role_enum_1.Role.COLLEGE_ADMIN, role_enum_1.Role.SUPER_ADMIN),
    (0, swagger_1.ApiOperation)({
        summary: 'Get a specific question paper by ID',
        description: 'Returns a specific question paper by ID with role-based access: Teachers can only access their own papers, College Admins can access papers from their college, Super Admins can access any paper.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Question paper ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns the question paper',
        schema: {
            type: 'object',
            properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                title: { type: 'string', example: 'Mathematics Final Exam 2024' },
                description: {
                    type: 'string',
                    example: 'Final examination for Mathematics course',
                },
                subjectId: {
                    type: 'object',
                    properties: {
                        _id: { type: 'string', example: '60d21b4667d0d8992e610c86' },
                        name: { type: 'string', example: 'Mathematics' },
                    },
                },
                topicId: {
                    type: 'object',
                    properties: {
                        _id: { type: 'string', example: '60d21b4667d0d8992e610c87' },
                        name: { type: 'string', example: 'Calculus' },
                    },
                },
                totalMarks: { type: 'number', example: 100 },
                duration: { type: 'number', example: 180 },
                instructions: {
                    type: 'string',
                    example: 'Answer all questions. Each question carries equal marks.',
                },
                questions: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            _id: { type: 'string', example: '60d21b4667d0d8992e610c88' },
                            content: {
                                type: 'string',
                                example: 'What is the derivative of f(x) = x²?',
                            },
                            options: {
                                type: 'array',
                                items: { type: 'string' },
                                example: ['2x', 'x', '2', '1'],
                            },
                            answer: { type: 'string', example: '2x' },
                            difficulty: { type: 'string', example: 'medium' },
                        },
                    },
                },
                generatedBy: { type: 'string', example: '60d21b4667d0d8992e610c90' },
                collegeId: { type: 'string', example: '60d21b4667d0d8992e610c91' },
                status: { type: 'string', example: 'active' },
                sections: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            name: { type: 'string', example: 'Section A' },
                            description: {
                                type: 'string',
                                example: 'Multiple Choice Questions',
                            },
                            order: { type: 'number', example: 1 },
                            sectionMarks: { type: 'number', example: 50 },
                            questions: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        question: {
                                            type: 'object',
                                            properties: {
                                                _id: { type: 'string', example: '60d21b4667d0d8992e610c88' },
                                                content: { type: 'string', example: 'What is the derivative of f(x) = x²?' },
                                                options: { type: 'array', items: { type: 'string' }, example: ['2x', 'x', '2', '1'] },
                                                answer: { type: 'string', example: '2x' },
                                                difficulty: { type: 'string', example: 'medium' },
                                                type: { type: 'string', example: 'multiple-choice' },
                                                marks: { type: 'number', example: 4 },
                                            },
                                        },
                                        order: { type: 'number', example: 1 },
                                    },
                                },
                            },
                        },
                    },
                },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Question paper not found' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], QuestionPapersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)(':id/download'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.TEACHER, role_enum_1.Role.COLLEGE_ADMIN, role_enum_1.Role.SUPER_ADMIN),
    (0, swagger_1.ApiOperation)({
        summary: 'Download a question paper in PDF or DOCX format',
        description: 'Download question papers with role-based access and download limits enforcement. Teachers can download their own papers, College Admins can download papers from their college, Super Admins can download any paper.',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Question paper ID' }),
    (0, swagger_1.ApiQuery)({
        name: 'format',
        enum: ['pdf', 'docx'],
        required: false,
        description: 'File format',
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns the file for download' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request - Invalid format' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Question paper not found' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Internal server error' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('format')),
    __param(2, (0, common_1.Req)()),
    __param(3, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object, Object]),
    __metadata("design:returntype", Promise)
], QuestionPapersController.prototype, "download", null);
__decorate([
    (0, common_1.Post)('limits'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN),
    (0, swagger_1.ApiOperation)({
        summary: 'Set question paper generation and download limits for a college',
        description: 'Sets the maximum number of question papers that can be generated and downloaded per college. Only super admins can set these limits.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Limit set successfully',
        schema: {
            type: 'object',
            properties: {
                collegeId: { type: 'string', example: '60d21b4667d0d8992e610c91' },
                subjectId: { type: 'string', example: '60d21b4667d0d8992e610c86' },
                maxQuestions: { type: 'number', example: 50 },
                message: { type: 'string', example: 'Question limit set successfully' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request - Invalid input data' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Not found - College or subject not found',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [set_question_limit_dto_1.SetQuestionLimitDto]),
    __metadata("design:returntype", void 0)
], QuestionPapersController.prototype, "setQuestionLimit", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.TEACHER),
    (0, swagger_1.ApiOperation)({
        summary: 'Update a question paper',
        description: 'Updates an existing question paper. Teachers can only update their own question papers.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Question paper ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Question paper updated successfully',
        schema: {
            type: 'object',
            properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                title: {
                    type: 'string',
                    example: 'Mathematics Final Exam 2024 (Updated)',
                },
                description: {
                    type: 'string',
                    example: 'Updated final examination for Mathematics course',
                },
                instructions: {
                    type: 'string',
                    example: 'Updated instructions. Answer all questions.',
                },
                updatedAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request - Invalid input data' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions or not the owner',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Question paper not found' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, update_question_paper_dto_1.UpdateQuestionPaperDto]),
    __metadata("design:returntype", void 0)
], QuestionPapersController.prototype, "update", null);
exports.QuestionPapersController = QuestionPapersController = QuestionPapersController_1 = __decorate([
    (0, swagger_1.ApiTags)('Question Papers'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Controller)('question-papers'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [question_papers_service_1.QuestionPapersService,
        services_1.TrackingService])
], QuestionPapersController);
//# sourceMappingURL=question-papers.controller.js.map