"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/app/admin/question-bank/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/admin/question-bank/page.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Breadcrumb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Breadcrumb */ \"(app-pages-browser)/./src/components/Breadcrumb.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_admin_question_bank_question_bank__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/question-bank/question-bank */ \"(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n // shadcn button\n // icon\n\n\nconst QuestionGenerationPage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold text-black\",\n                                    children: \"Question Bank\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\question-bank\\\\page.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 9\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Breadcrumb__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    items: [\n                                        {\n                                            label: 'Home',\n                                            href: '/'\n                                        },\n                                        {\n                                            label: '...',\n                                            href: '#'\n                                        },\n                                        {\n                                            label: 'Question Bank'\n                                        }\n                                    ],\n                                    className: \"text-sm mt-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\question-bank\\\\page.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 9\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\question-bank\\\\page.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 7\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                            href: \"/admin/add-question\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white\",\n                                children: [\n                                    \"Add Questions\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\question-bank\\\\page.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 9\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\question-bank\\\\page.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 7\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\question-bank\\\\page.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 7\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\question-bank\\\\page.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 3\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto py-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_question_bank_question_bank__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\question-bank\\\\page.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 7\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\question-bank\\\\page.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 5\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\question-bank\\\\page.tsx\",\n            lineNumber: 15,\n            columnNumber: 3\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\question-bank\\\\page.tsx\",\n        lineNumber: 14,\n        columnNumber: 1\n    }, undefined);\n};\n_c = QuestionGenerationPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuestionGenerationPage);\nvar _c;\n$RefreshReg$(_c, \"QuestionGenerationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/question-bank/page.tsx\n"));

/***/ })

});