"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/edit-question/[id]/page",{

/***/ "(app-pages-browser)/./src/components/admin/edit-question-form.tsx":
/*!*****************************************************!*\
  !*** ./src/components/admin/edit-question-form.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditQuestionForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/api/subjects */ \"(app-pages-browser)/./src/lib/api/subjects.ts\");\n/* harmony import */ var _lib_api_questions__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api/questions */ \"(app-pages-browser)/./src/lib/api/questions.ts\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/utils/imageUtils */ \"(app-pages-browser)/./src/utils/imageUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB\n;\nconst ACCEPTED_FILE_TYPES = [\n    \"image/jpeg\",\n    \"image/jpg\",\n    \"image/png\",\n    \"image/svg+xml\"\n];\n// Form schema with custom validation for options\nconst formSchema = zod__WEBPACK_IMPORTED_MODULE_17__.z.object({\n    subject: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a subject\"\n    }),\n    topic: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(1, {\n        message: \"Please select a topic\"\n    }),\n    questionText: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().min(5, {\n        message: \"Question must be at least 5 characters\"\n    }),\n    optionA: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionB: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionC: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    optionD: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    correctAnswer: zod__WEBPACK_IMPORTED_MODULE_17__.z.enum([\n        \"A\",\n        \"B\",\n        \"C\",\n        \"D\"\n    ], {\n        required_error: \"Please select the correct answer\"\n    }),\n    explanation: zod__WEBPACK_IMPORTED_MODULE_17__.z.string().optional(),\n    difficulty: zod__WEBPACK_IMPORTED_MODULE_17__.z.enum([\n        \"Easy\",\n        \"Medium\",\n        \"Hard\"\n    ], {\n        required_error: \"Please select a difficulty level\"\n    })\n});\nfunction EditQuestionForm(param) {\n    let { questionData, questionId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [questionImage, setQuestionImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [optionImages, setOptionImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: null,\n        B: null,\n        C: null,\n        D: null\n    });\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topics, setTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [optionValidationErrors, setOptionValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        A: false,\n        B: false,\n        C: false,\n        D: false\n    });\n    // Refs for file inputs\n    const questionImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const optionImageRefs = {\n        A: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        B: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        C: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        D: (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null)\n    };\n    // Initialize form with question data\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(formSchema),\n        defaultValues: {\n            subject: \"\",\n            topic: \"\",\n            questionText: \"\",\n            optionA: \"\",\n            optionB: \"\",\n            optionC: \"\",\n            optionD: \"\",\n            correctAnswer: \"\",\n            explanation: \"\",\n            difficulty: \"\"\n        }\n    });\n    // Parse existing question data and populate form\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditQuestionForm.useEffect\": ()=>{\n            if (questionData && subjects.length > 0) {\n                var _questionData_options;\n                // Parse options from the question data\n                const parseOptions = {\n                    \"EditQuestionForm.useEffect.parseOptions\": ()=>{\n                        if (!questionData.options || questionData.options.length === 0) return [\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\"\n                        ];\n                        if (typeof questionData.options[0] === 'string') {\n                            if (questionData.options.length === 1 && questionData.options[0].includes(',')) {\n                                // Single comma-separated string\n                                const optionTexts = questionData.options[0].split(',');\n                                return optionTexts.map({\n                                    \"EditQuestionForm.useEffect.parseOptions\": (text)=>text.trim()\n                                }[\"EditQuestionForm.useEffect.parseOptions\"]).concat(Array(4 - optionTexts.length).fill(\"\")).slice(0, 4);\n                            } else {\n                                // Array of individual strings\n                                return questionData.options.concat(Array(4 - questionData.options.length).fill(\"\")).slice(0, 4);\n                            }\n                        }\n                        return [\n                            \"\",\n                            \"\",\n                            \"\",\n                            \"\"\n                        ];\n                    }\n                }[\"EditQuestionForm.useEffect.parseOptions\"];\n                const parsedOptions = parseOptions();\n                const newOptionImages = {\n                    A: null,\n                    B: null,\n                    C: null,\n                    D: null\n                };\n                // Check for base64 images in options and extract them\n                parsedOptions.forEach({\n                    \"EditQuestionForm.useEffect\": (option, index)=>{\n                        const optionKey = String.fromCharCode(65 + index); // A, B, C, D\n                        if (typeof option === 'string' && (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_16__.isBase64Image)(option)) {\n                            newOptionImages[optionKey] = (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_16__.ensureDataUrl)(option);\n                            parsedOptions[index] = \"\"; // Clear text since it's an image\n                        }\n                    }\n                }[\"EditQuestionForm.useEffect\"]);\n                setOptionImages(newOptionImages);\n                var _questionData_options_findIndex;\n                // Find correct answer letter\n                const answerIndex = (_questionData_options_findIndex = (_questionData_options = questionData.options) === null || _questionData_options === void 0 ? void 0 : _questionData_options.findIndex({\n                    \"EditQuestionForm.useEffect\": (opt)=>opt === questionData.answer\n                }[\"EditQuestionForm.useEffect\"])) !== null && _questionData_options_findIndex !== void 0 ? _questionData_options_findIndex : -1;\n                const correctAnswerLetter = answerIndex >= 0 ? String.fromCharCode(65 + answerIndex) : \"A\";\n                // Handle topicId - it could be a string ID or a populated object\n                const topicId = questionData.topicId ? typeof questionData.topicId === 'string' ? questionData.topicId : questionData.topicId._id : \"\";\n                console.log(\"Question data:\", questionData);\n                console.log(\"Topic ID extracted:\", topicId);\n                console.log(\"Available subjects:\", subjects);\n                // Set topics for the selected subject FIRST\n                const selectedSubject = subjects.find({\n                    \"EditQuestionForm.useEffect.selectedSubject\": (s)=>s._id === questionData.subjectId._id\n                }[\"EditQuestionForm.useEffect.selectedSubject\"]);\n                console.log(\"Selected subject:\", selectedSubject);\n                if (selectedSubject) {\n                    setTopics(selectedSubject.topics || []);\n                    console.log(\"Set topics:\", selectedSubject.topics);\n                }\n                // Set form values AFTER topics are set\n                form.reset({\n                    subject: questionData.subjectId._id,\n                    topic: topicId,\n                    questionText: questionData.content,\n                    optionA: parsedOptions[0] || \"\",\n                    optionB: parsedOptions[1] || \"\",\n                    optionC: parsedOptions[2] || \"\",\n                    optionD: parsedOptions[3] || \"\",\n                    correctAnswer: correctAnswerLetter,\n                    explanation: questionData.explanation || \"\",\n                    difficulty: questionData.difficulty.charAt(0).toUpperCase() + questionData.difficulty.slice(1)\n                });\n                console.log(\"Form reset with topic:\", topicId);\n            }\n        }\n    }[\"EditQuestionForm.useEffect\"], [\n        questionData,\n        subjects,\n        form\n    ]);\n    // Fetch subjects and topics from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditQuestionForm.useEffect\": ()=>{\n            const fetchSubjectsAndTopics = {\n                \"EditQuestionForm.useEffect.fetchSubjectsAndTopics\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const data = await (0,_lib_api_subjects__WEBPACK_IMPORTED_MODULE_14__.getSubjectsWithTopics)();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects and topics:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                            title: \"Error\",\n                            description: error.message || \"Failed to load subjects and topics\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"EditQuestionForm.useEffect.fetchSubjectsAndTopics\"];\n            fetchSubjectsAndTopics();\n        }\n    }[\"EditQuestionForm.useEffect\"], []);\n    // Handle subject change to update topics\n    const handleSubjectChange = (value)=>{\n        form.setValue(\"subject\", value);\n        form.setValue(\"topic\", \"\");\n        // Find the selected subject and set its topics\n        const selectedSubject = subjects.find((subject)=>subject._id === value);\n        if (selectedSubject) {\n            setTopics(selectedSubject.topics || []);\n        } else {\n            setTopics([]);\n        }\n    };\n    // Handle image upload\n    const handleImageUpload = (e, type)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            if (type === \"question\") {\n                var _event_target;\n                setQuestionImage((_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result);\n            } else {\n                setOptionImages((prev)=>{\n                    var _event_target;\n                    return {\n                        ...prev,\n                        [type]: (_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result\n                    };\n                });\n                // Clear validation error for this option\n                setOptionValidationErrors((prev)=>({\n                        ...prev,\n                        [type]: false\n                    }));\n            }\n        };\n        reader.readAsDataURL(file);\n    };\n    // Remove image\n    const removeImage = (type)=>{\n        if (type === \"question\") {\n            setQuestionImage(null);\n            if (questionImageRef.current) {\n                questionImageRef.current.value = \"\";\n            }\n        } else {\n            var _optionImageRefs_type;\n            setOptionImages((prev)=>({\n                    ...prev,\n                    [type]: null\n                }));\n            if ((_optionImageRefs_type = optionImageRefs[type]) === null || _optionImageRefs_type === void 0 ? void 0 : _optionImageRefs_type.current) {\n                optionImageRefs[type].current.value = \"\";\n            }\n            // Clear form validation error for this option if it exists\n            form.clearErrors(\"option\".concat(type));\n            // Update validation state\n            setOptionValidationErrors((prev)=>({\n                    ...prev,\n                    [type]: false\n                }));\n        }\n    };\n    // Custom validation function\n    const validateOptions = (formData)=>{\n        const errors = [];\n        const validationState = {};\n        const options = [\n            'A',\n            'B',\n            'C',\n            'D'\n        ];\n        for (const option of options){\n            const hasText = formData[\"option\".concat(option)] && formData[\"option\".concat(option)].trim() !== '';\n            const hasImage = optionImages[option] !== null;\n            if (!hasText && !hasImage) {\n                errors.push(\"Option \".concat(option, \" must have either text or an image\"));\n                validationState[option] = true;\n            } else {\n                validationState[option] = false;\n            }\n        }\n        // Update validation state\n        setOptionValidationErrors(validationState);\n        return errors;\n    };\n    // Handle form submission\n    const onSubmit = async (data)=>{\n        setIsSubmitting(true);\n        try {\n            var _data_optionA, _data_optionB, _data_optionC, _data_optionD;\n            console.log(\"Form data:\", data);\n            // Validate that each option has either text or image\n            const validationErrors = validateOptions(data);\n            if (validationErrors.length > 0) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                    title: \"Validation Error\",\n                    description: validationErrors.join(', '),\n                    variant: \"destructive\"\n                });\n                setIsSubmitting(false);\n                return;\n            }\n            // Convert option data to array format expected by API\n            // Use image base64 if no text, otherwise use text\n            const options = [\n                ((_data_optionA = data.optionA) === null || _data_optionA === void 0 ? void 0 : _data_optionA.trim()) || optionImages.A || '',\n                ((_data_optionB = data.optionB) === null || _data_optionB === void 0 ? void 0 : _data_optionB.trim()) || optionImages.B || '',\n                ((_data_optionC = data.optionC) === null || _data_optionC === void 0 ? void 0 : _data_optionC.trim()) || optionImages.C || '',\n                ((_data_optionD = data.optionD) === null || _data_optionD === void 0 ? void 0 : _data_optionD.trim()) || optionImages.D || ''\n            ];\n            // Map correctAnswer (A, B, C, D) to the actual option value\n            const answerMap = {\n                A: 0,\n                B: 1,\n                C: 2,\n                D: 3\n            };\n            const answerIndex = answerMap[data.correctAnswer];\n            const answer = options[answerIndex];\n            // Convert difficulty to lowercase to match API expectations\n            const difficulty = data.difficulty.toLowerCase();\n            // Create base question data\n            const baseQuestionData = {\n                content: data.questionText,\n                options,\n                answer,\n                subjectId: data.subject,\n                topicId: data.topic,\n                difficulty,\n                type: \"multiple-choice\"\n            };\n            // Only add explanation if it has a value\n            const questionData = data.explanation && data.explanation.trim() !== '' ? {\n                ...baseQuestionData,\n                explanation: data.explanation\n            } : baseQuestionData;\n            console.log(\"Prepared question data:\", questionData);\n            // The backend expects images embedded as base64 in options, not as separate files\n            console.log(\"Updating question with embedded base64 images\");\n            let finalQuestionData = {\n                ...questionData\n            };\n            // If question has an image, embed it in the question text as base64\n            if (questionImage) {\n                finalQuestionData.content = \"\".concat(questionData.content, \"\\n\").concat(questionImage);\n            }\n            // Submit to API using JSON (the options already contain base64 images where needed)\n            await (0,_lib_api_questions__WEBPACK_IMPORTED_MODULE_15__.updateQuestion)(questionId, finalQuestionData);\n            // Display success toast\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                title: \"Question Updated\",\n                description: \"Your question has been successfully updated.\"\n            });\n            // Redirect back to question bank\n            router.push(\"/admin/question-bank\");\n        } catch (error) {\n            console.error(\"Error updating question:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_13__.toast)({\n                title: \"Error\",\n                description: error.message || \"Failed to update question. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Cancel and go back\n    const handleCancel = ()=>{\n        router.push(\"/admin/question-bank\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                lineNumber: 395,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n            lineNumber: 394,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n            className: \"w-full max-w-4xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.Form, {\n                    ...form,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: form.handleSubmit(onSubmit),\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                        control: form.control,\n                                        name: \"subject\",\n                                        render: (param)=>{\n                                            let { field } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                        children: \"Subject *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                        onValueChange: handleSubjectChange,\n                                                        value: field.value,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                        placeholder: \"Select a subject\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 417,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                children: subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                        value: subject._id,\n                                                                        children: subject.name\n                                                                    }, subject._id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 422,\n                                                                        columnNumber: 29\n                                                                    }, void 0))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 21\n                                            }, void 0);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                        control: form.control,\n                                        name: \"topic\",\n                                        render: (param)=>{\n                                            let { field } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                        children: \"Topic *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                        onValueChange: field.onChange,\n                                                        value: field.value,\n                                                        disabled: topics.length === 0,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                        placeholder: topics.length === 0 ? \"Select a subject first\" : \"Select a topic\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                                children: topics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                        value: topic._id,\n                                                                        children: topic.name\n                                                                    }, topic._id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 447,\n                                                                        columnNumber: 29\n                                                                    }, void 0))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 21\n                                            }, void 0);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"questionText\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: \"Question Text *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__.Textarea, {\n                                                    placeholder: \"Enter your question here...\",\n                                                    className: \"min-h-[100px]\",\n                                                    ...field\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                        children: \"Question Image (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                onClick: ()=>{\n                                                    var _questionImageRef_current;\n                                                    return (_questionImageRef_current = questionImageRef.current) === null || _questionImageRef_current === void 0 ? void 0 : _questionImageRef_current.click();\n                                                },\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Upload Image\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                ref: questionImageRef,\n                                                type: \"file\",\n                                                accept: ACCEPTED_FILE_TYPES.join(\",\"),\n                                                onChange: (e)=>handleImageUpload(e, \"question\"),\n                                                className: \"hidden\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 19\n                                            }, this),\n                                            questionImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: questionImage,\n                                                        alt: \"Question\",\n                                                        width: 100,\n                                                        height: 100,\n                                                        className: \"rounded-md object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"destructive\",\n                                                        size: \"icon\",\n                                                        className: \"absolute -top-2 -right-2 h-6 w-6\",\n                                                        onClick: ()=>removeImage(\"question\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                        children: \"Answer Options *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            \"A\",\n                                            \"B\",\n                                            \"C\",\n                                            \"D\"\n                                        ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                                        control: form.control,\n                                                        name: \"option\".concat(option),\n                                                        render: (param)=>{\n                                                            let { field } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                        children: [\n                                                                            \"Option \",\n                                                                            option,\n                                                                            optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-green-600 ml-2\",\n                                                                                children: \"(Image uploaded)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                                lineNumber: 535,\n                                                                                columnNumber: 33\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 532,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                                                            placeholder: optionImages[option] ? \"Option \".concat(option, \" text (optional - image uploaded)\") : \"Enter option \".concat(option, \" text or upload an image...\"),\n                                                                            ...field\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                            lineNumber: 539,\n                                                                            columnNumber: 31\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 538,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 548,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    optionValidationErrors[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-red-600\",\n                                                                        children: [\n                                                                            \"Option \",\n                                                                            option,\n                                                                            \" requires either text or an image\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 550,\n                                                                        columnNumber: 31\n                                                                    }, void 0)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 531,\n                                                                columnNumber: 27\n                                                            }, void 0);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>{\n                                                                    var _optionImageRefs_option_current, _optionImageRefs_option;\n                                                                    return (_optionImageRefs_option = optionImageRefs[option]) === null || _optionImageRefs_option === void 0 ? void 0 : (_optionImageRefs_option_current = _optionImageRefs_option.current) === null || _optionImageRefs_option_current === void 0 ? void 0 : _optionImageRefs_option_current.click();\n                                                                },\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 567,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Image\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                ref: optionImageRefs[option],\n                                                                type: \"file\",\n                                                                accept: ACCEPTED_FILE_TYPES.join(\",\"),\n                                                                onChange: (e)=>handleImageUpload(e, option),\n                                                                className: \"hidden\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            optionImages[option] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                        src: optionImages[option],\n                                                                        alt: \"Option \".concat(option),\n                                                                        width: 60,\n                                                                        height: 60,\n                                                                        className: \"rounded-md object-cover\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 579,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        type: \"button\",\n                                                                        variant: \"destructive\",\n                                                                        size: \"icon\",\n                                                                        className: \"absolute -top-1 -right-1 h-4 w-4\",\n                                                                        onClick: ()=>removeImage(option),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-2 w-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                            lineNumber: 593,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                        lineNumber: 586,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, option, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"correctAnswer\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: \"Correct Answer *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_9__.RadioGroup, {\n                                                    onValueChange: field.onChange,\n                                                    value: field.value,\n                                                    className: \"flex flex-row space-x-6\",\n                                                    children: [\n                                                        \"A\",\n                                                        \"B\",\n                                                        \"C\",\n                                                        \"D\"\n                                                    ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_9__.RadioGroupItem, {\n                                                                    value: option,\n                                                                    id: option\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 618,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                                    htmlFor: option,\n                                                                    children: [\n                                                                        \"Option \",\n                                                                        option\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 619,\n                                                                    columnNumber: 29\n                                                                }, void 0)\n                                                            ]\n                                                        }, option, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 27\n                                                        }, void 0))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"difficulty\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: \"Difficulty Level *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                onValueChange: field.onChange,\n                                                value: field.value,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                                placeholder: \"Select difficulty level\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 639,\n                                                                columnNumber: 27\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 25\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"Easy\",\n                                                                children: \"Easy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"Medium\",\n                                                                children: \"Medium\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 644,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"Hard\",\n                                                                children: \"Hard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 645,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormField, {\n                                control: form.control,\n                                name: \"explanation\",\n                                render: (param)=>{\n                                    let { field } = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormItem, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormLabel, {\n                                                children: [\n                                                    \"Explanation (Optional)\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.TooltipTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"inline h-4 w-4 ml-1 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 663,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 662,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_12__.TooltipContent, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Provide an explanation for the correct answer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                    lineNumber: 666,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__.Textarea, {\n                                                    placeholder: \"Explain why this is the correct answer...\",\n                                                    className: \"min-h-[80px]\",\n                                                    ...field\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_7__.FormMessage, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 658,\n                                        columnNumber: 19\n                                    }, void 0);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 654,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-4 pt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        onClick: handleCancel,\n                                        disabled: isSubmitting,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Info_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Updating...\"\n                                            ]\n                                        }, void 0, true) : \"Update Question\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                                lineNumber: 683,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n                lineNumber: 403,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n            lineNumber: 402,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\edit-question-form.tsx\",\n        lineNumber: 401,\n        columnNumber: 5\n    }, this);\n}\n_s(EditQuestionForm, \"F/d5wKHUivNzAWSLkvfvZ9N8nyk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm\n    ];\n});\n_c = EditQuestionForm;\nvar _c;\n$RefreshReg$(_c, \"EditQuestionForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/edit-question-form.tsx\n"));

/***/ })

});