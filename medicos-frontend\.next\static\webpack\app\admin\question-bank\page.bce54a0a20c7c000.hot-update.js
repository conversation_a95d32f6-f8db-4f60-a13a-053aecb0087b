"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx":
/*!**************************************************************!*\
  !*** ./src/components/admin/question-bank/question-bank.tsx ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuestionBank)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _question_list__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./question-list */ \"(app-pages-browser)/./src/components/admin/question-bank/question-list.tsx\");\n/* harmony import */ var _components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/pagination */ \"(app-pages-browser)/./src/components/ui/pagination.tsx\");\n/* harmony import */ var _question_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./question-skeleton */ \"(app-pages-browser)/./src/components/admin/question-bank/question-skeleton.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction QuestionBank() {\n    _s();\n    // State for subjects and topics\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [topics, setTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // State for filters\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_subjects\");\n    const [selectedTopic, setSelectedTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all_topics\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // State for questions and pagination\n    const [questions, setQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch subjects with topics\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchSubjectsWithTopics = {\n                \"QuestionBank.useEffect.fetchSubjectsWithTopics\": async ()=>{\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        const response = await fetch(\"\".concat(baseUrl, \"/subjects/with-topics\"), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch subjects: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setSubjects(data);\n                    } catch (error) {\n                        console.error(\"Error fetching subjects:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load subjects. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchSubjectsWithTopics\"];\n            fetchSubjectsWithTopics();\n        }\n    }[\"QuestionBank.useEffect\"], []);\n    // Update topics when subject changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            if (selectedSubject && selectedSubject !== \"all_subjects\") {\n                const selectedSubjectObj = subjects.find({\n                    \"QuestionBank.useEffect.selectedSubjectObj\": (s)=>s._id === selectedSubject\n                }[\"QuestionBank.useEffect.selectedSubjectObj\"]);\n                if (selectedSubjectObj && selectedSubjectObj.topics) {\n                    setTopics(selectedSubjectObj.topics);\n                } else {\n                    setTopics([]);\n                }\n                setSelectedTopic(\"all_topics\");\n            } else {\n                setTopics([]);\n            }\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        subjects\n    ]);\n    // Fetch questions with filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuestionBank.useEffect\": ()=>{\n            const fetchQuestions = {\n                \"QuestionBank.useEffect.fetchQuestions\": async ()=>{\n                    setLoading(true);\n                    try {\n                        const baseUrl = \"http://localhost:3000/api\" || 0;\n                        // Build query parameters\n                        const params = new URLSearchParams();\n                        if (selectedSubject && selectedSubject !== \"all_subjects\") params.append('subjectId', selectedSubject);\n                        if (selectedTopic && selectedTopic !== \"all_topics\") params.append('topicId', selectedTopic);\n                        if (searchQuery) params.append('search', searchQuery);\n                        params.append('page', pagination.currentPage.toString());\n                        params.append('limit', pageSize.toString());\n                        const response = await fetch(\"\".concat(baseUrl, \"/questions?\").concat(params.toString()), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch questions: \".concat(response.status));\n                        }\n                        const data = await response.json();\n                        setQuestions(data.questions);\n                        setPagination(data.pagination);\n                    } catch (error) {\n                        console.error(\"Error fetching questions:\", error);\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: \"Error\",\n                            description: \"Failed to load questions. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"QuestionBank.useEffect.fetchQuestions\"];\n            fetchQuestions();\n        }\n    }[\"QuestionBank.useEffect\"], [\n        selectedSubject,\n        selectedTopic,\n        searchQuery,\n        pagination.currentPage,\n        pageSize\n    ]);\n    // Handle page change\n    const handlePageChange = (pageNumber)=>{\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: pageNumber\n            }));\n    };\n    // Handle page size change\n    const handlePageSizeChange = (newPageSize)=>{\n        setPageSize(newPageSize);\n        setPagination((prev)=>({\n                ...prev,\n                currentPage: 1,\n                itemsPerPage: newPageSize\n            }));\n    };\n    // Handle difficulty change\n    const handleDifficultyChange = async (questionId, difficulty)=>{\n        try {\n            const baseUrl = \"http://localhost:3000/api\" || 0;\n            const response = await fetch(\"\".concat(baseUrl, \"/questions/\").concat(questionId), {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"backendToken\"))\n                },\n                body: JSON.stringify({\n                    difficulty\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update question: \".concat(response.status));\n            }\n            // Update local state\n            setQuestions((prev)=>prev.map((q)=>q._id === questionId ? {\n                        ...q,\n                        difficulty\n                    } : q));\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Success\",\n                description: \"Question difficulty updated successfully\"\n            });\n        } catch (error) {\n            console.error(\"Error updating question difficulty:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                title: \"Error\",\n                description: \"Failed to update question difficulty\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Format questions for the QuestionList component\n    const formattedQuestions = questions.map((q)=>{\n        var _topics_find;\n        let parsedOptions = [];\n        if (q.options && q.options.length > 0) {\n            if (typeof q.options[0] === 'string') {\n                // Check if it's a single comma-separated string or an array of individual strings\n                if (q.options.length === 1 && q.options[0].includes(',')) {\n                    // Single comma-separated string: [\"Paris,London,Berlin,Madrid\"]\n                    const optionTexts = q.options[0].split(',');\n                    parsedOptions = optionTexts.map((text, index)=>({\n                            label: String.fromCharCode(97 + index),\n                            text: text.trim()\n                        }));\n                } else {\n                    // Array of individual strings: [\"Cerebrum\", \"Cerebellum\", \"Medulla\", \"Pons\"]\n                    parsedOptions = q.options.map((text, index)=>({\n                            label: String.fromCharCode(97 + index),\n                            text: text.trim()\n                        }));\n                }\n            } else {\n                // If options is already an array of objects\n                parsedOptions = q.options.map((opt, index)=>({\n                        label: String.fromCharCode(97 + index),\n                        text: typeof opt === 'string' ? opt : opt.text || '',\n                        imageUrl: typeof opt === 'object' ? opt.imageUrl : undefined\n                    }));\n            }\n        }\n        return {\n            id: q._id,\n            subject: q.subjectId.name,\n            topic: ((_topics_find = topics.find((t)=>t._id === q.topicId)) === null || _topics_find === void 0 ? void 0 : _topics_find.name) || \"Unknown\",\n            text: q.content,\n            options: parsedOptions,\n            difficulty: q.difficulty.charAt(0).toUpperCase() + q.difficulty.slice(1),\n            correctAnswer: q.answer,\n            reviewStatus: q.reviewStatus\n        };\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Subject\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedSubject,\n                                onValueChange: setSelectedSubject,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: \"Select Subject\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_subjects\",\n                                                children: \"All Subjects\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 15\n                                            }, this),\n                                            subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: subject._id,\n                                                    children: subject.name\n                                                }, subject._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Topic\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                value: selectedTopic,\n                                onValueChange: setSelectedTopic,\n                                disabled: selectedSubject === \"all_subjects\" || topics.length === 0,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                                            placeholder: selectedSubject !== \"all_subjects\" ? \"Select Topic\" : \"Select Subject First\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                value: \"all_topics\",\n                                                children: \"All Topics\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this),\n                                            topics.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                                    value: topic._id,\n                                                    children: topic.name\n                                                }, topic._id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1\",\n                                children: \"Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        type: \"text\",\n                                        placeholder: \"Search questions...\",\n                                        className: \"pl-8\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_skeleton__WEBPACK_IMPORTED_MODULE_6__.QuestionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 284,\n                columnNumber: 9\n            }, this) : formattedQuestions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_question_list__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        questions: formattedQuestions,\n                        onDifficultyChange: handleDifficultyChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__.Pagination, {\n                        currentPage: pagination.currentPage,\n                        totalPages: pagination.totalPages,\n                        pageSize: pageSize,\n                        totalItems: pagination.totalItems,\n                        onPageChange: handlePageChange,\n                        onPageSizeChange: handlePageSizeChange,\n                        pageSizeOptions: [\n                            5,\n                            10,\n                            20,\n                            50\n                        ]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"No questions found. Try adjusting your filters.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n                lineNumber: 305,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\admin\\\\question-bank\\\\question-bank.tsx\",\n        lineNumber: 226,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionBank, \"vuZocBv73CrThW8YbI7M7kNO/js=\");\n_c = QuestionBank;\nvar _c;\n$RefreshReg$(_c, \"QuestionBank\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/question-bank/question-bank.tsx\n"));

/***/ })

});